package com.yunqu.xty.inf;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.IBaseService;
import com.yq.busi.common.model.UserModel;
import com.yunqu.xty.base.CommonLogger;
import com.yunqu.xty.base.Constants;
import com.yunqu.xty.service.InvokeAlService;
import com.yunqu.xty.utils.ClassTools;

import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 同步es订单信息
 */
public class InvokeAiService extends IBaseService{

		private Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("ai-service").getName());



	@Override
	public String getServiceId() {
		return "XTY_INVOKE_AI_SERVICE";
	}

	@Override
	public String getName() {
		return Constants.APP_NAME;
	}

	@Override
	public JSONObject invokeMethod(JSONObject param) throws ServiceException {
		String orderId = param.getString("orderId");
		String schema = param.getString("schema");
		String entId = param.getString("entId");
		String busiOrderId = param.getString("busiOrderId");
		//执行同步ES操作
        logger.info("[" + orderId + "]开始调用大模型");
		UserModel user = new UserModel();
		user.setEpCode(entId);
		user.setBusiOrderId(busiOrderId);
		user.setUserAcc("system");
		user.setUserName("系统");
		user.setSchemaName(schema);
		ClassTools.getClass(InvokeAlService.class).invoke(user,orderId,schema);
		logger.info("[" + orderId + "]结束调用大模型");
		return EasyResult.ok();
	}


}
