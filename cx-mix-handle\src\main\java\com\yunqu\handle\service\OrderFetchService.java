package com.yunqu.handle.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.AttachmentUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.oss.OSSTools;
import com.yunqu.cc.oss.model.AttachmentDbinfo;
import com.yunqu.cc.oss.model.AttachmentFile;
import com.yunqu.cc.oss.model.OSSAttachmentUtil;
import com.yunqu.handle.base.BaseService;
import com.yunqu.handle.base.CommonLogger;
import com.yunqu.handle.base.Constants;
import com.yunqu.handle.base.QueryFactory;
import com.yunqu.handle.util.OkHttpUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工单拉取服务
 * 负责调用OutInterfaceServlet拉取工单信息并写入数据库
 */
public class OrderFetchService extends BaseService{
    
    private static final Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("order").getName());
    
    /**
     * 拉取工单数据
     * @param pageIndex
     * @param pageSize
     * @param ids
     * @param force
     * @return
     */
    public JSONObject fetchOrders(int pageIndex, int pageSize,String ids,boolean force,String StartTime,String endTime) {
        try {
            // 构建请求参数
            JSONObject requestData = new JSONObject();
            requestData.put("command", "fetchOrder");
            requestData.put("pageIndex", pageIndex);
            requestData.put("pageSize", pageSize);
            requestData.put("appId", Constants.getAppId());
            if (StringUtils.isNotBlank(ids)) {
                requestData.put("ids", ids);
            }
            if (force) {
                requestData.put("force", true);
            }
            if (StringUtils.isNotBlank(StartTime)) {
                requestData.put("startTime", StartTime);
            }
            if (StringUtils.isNotBlank(endTime)) {
                requestData.put("endTime", endTime);
            }
            // 添加时间戳
            String timestamp = String.valueOf(System.currentTimeMillis());
            requestData.put("timestamp", timestamp);
            
            // 计算签名
            String secretKey = Constants.getSysSecretKey();
            if (StringUtils.isBlank(secretKey)) {
                logger.error("系统密钥未配置");
                return createErrorResult("系统密钥未配置");
            }
            
            String signature = calculateSignature(requestData, secretKey);
            requestData.put("signature", signature);
            
            logger.info("发送请求到{}: {}",getOutInterfaceUrl(), requestData.toJSONString());
            
            // 发送HTTP请求
            String response = OkHttpUtil.getInstance().postSync(getOutInterfaceUrl(), requestData.toJSONString(),"application/json");
            logger.info("收到{}响应: {}",getOutInterfaceUrl(), response);
            if (StringUtils.isNotBlank(response)) {
                JSONObject result = JSONObject.parseObject(response);
                return result;
            } else {
                logger.error("OutInterfaceServlet返回空响应");
                return createErrorResult("OutInterfaceServlet返回空响应");
            }
            
        } catch (Exception e) {
            logger.error("调用OutInterfaceServlet失败: " + e.getMessage(), e);
            return createErrorResult("调用OutInterfaceServlet失败: " + e.getMessage());
        }
    }


     public JSONObject checkFiles () throws SQLException {
        EasyQuery query = QueryFactory.getQuery();
        EasySQL sql = new EasySQL();
        sql.append(" select * from " + Constants.getSysSchema() + ".c_box_appeal ");
        sql.append("01"," where EXIST_ATTACHMENT = ? ");
        sql.append(" and APPEAL_ATTACHMENT is not null ");
        sql.append(DateUtil.addHour(DateUtil.TIME_FORMAT, DateUtil.getCurrentDateStr(), -12)," and CREATE_TIME >= ? ");
        sql.append(DateUtil.getCurrentDateStr()," and CREATE_TIME <= ? ");
        sql.append(" order by CREATE_TIME asc");
        logger.info("查询存在附件的工单数量: {}",sql.getFullSq());

        List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
        if (CollectionUtils.isNotEmpty(list)) {
            for (JSONObject jsonObject : list) {
                String appealAttachment = jsonObject.getString("APPEAL_ATTACHMENT");
                JSONObject attachmentResult = getAttachmentListFromInterface(appealAttachment);
                if (attachmentResult != null && attachmentResult.getBooleanValue("success")) {
                    JSONArray attachments = attachmentResult.getJSONArray("data");
                    if (attachments != null && !attachments.isEmpty()) {
                        List<JSONObject> fileList = getFileList(appealAttachment);
                        if (CollectionUtils.isNotEmpty(fileList)){
                            if (attachments.size() == fileList.size()) {
                                continue;
                            }
                            Map fileMap = fileList.stream().collect(Collectors.toMap(item -> item.getString("NAME"),item -> item,(o1,o2) -> o2));
                            for (int i = 0; i < attachments.size(); i++) {
                                JSONObject attachment = attachments.getJSONObject(i);
                                if (fileMap.get(attachment.getString("fileName")) == null) {
                                    logger.info("文件不存在，文件名称: {}, 附件ID: {}",attachment.getString("fileName"),attachment.getString("id"));
                                    try {
                                        // 通过getFile接口获取文件并上传到S3
                                        downloadAndUploadAttachment(attachment, appealAttachment, Constants.getEntId(), Constants.getBusiOrderId());
                                    } catch (Exception e) {
                                        logger.error("上传附件到S3失败，附件ID: {}, 错误: {}", attachment.getString("id"), e.getMessage(), e);
                                    }
                                }
                            }
                        }
                    }
                    
                }
            }
        } 
        return null;
    }



    private List<JSONObject> getFileList (String busiId) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append(" select * from " + Constants.getSysSchema() + ".c_cf_attachment");
        sql.append(" where 1=1 ");
        sql.append(busiId," and BUSI_ID =? ",false);
        return QueryFactory.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());

    }

    /**
     * 拉取工单数据
     * @param pageIndex 页码
     * @param pageSize 每页大小
     * @return 拉取结果
     */
    public JSONObject fetchOrders(int pageIndex, int pageSize) {
        return fetchOrders(pageIndex, pageSize,"",false,"","");
    }

    /**
     * 拉取工单数据
     * @param pageIndex 页码
     * @param pageSize 每页大小
     * @param force 是否强制拉取
     * @return 拉取结果
     */
    public JSONObject fetchOrders(int pageIndex, int pageSize,boolean force) {
        return fetchOrders(pageIndex, pageSize,"",force,"","");
    }

    public JSONObject fetchOrders(int pageIndex, int pageSize,boolean force,String startTime,String endTime) {
        return fetchOrders(pageIndex, pageSize,"",force,startTime,endTime);
    }
    
    /**
     * 将工单数据保存到数据库（批量插入）
     * @param dataList 工单数据列表
     */
    public void saveOrdersToDatabase(List<JSONObject> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        
        List<String> savedOrderIds = new ArrayList<>();
        List<JSONObject> failedOrders = new ArrayList<>();
        int batchSize = 50; // 批量处理大小
        
        try {
            EasyQuery query = QueryFactory.getQuery();
            
            // 构建插入SQL
            String insertSql = "INSERT INTO  "+Constants.getSysSchema()+".c_box_appeal (" +
                    "ID, M_ID, ENT_ID, BUSI_ORDER_ID, CREATE_TIME, ENT_TYPE, " +
                    "ENT_DEPT_CODE, ENT_DEPT_NAME, PROVINCE_CODE, PROVINCE_NAME, " +
                    "CITY_CODE, CITY_NAME, IS_COMPLAINED, IS_BACK_RESULT, " +
                    "IS_SATISFY_RESULT, APPEAL_TIME, APPEAL_NAME, CARD_TYPE, " +
                    "ID_CARD, PHONE, APPEAL_PHONE, APPEAL_CONTENT, " +
                    "APPEAL_ATTACHMENT, APPEAL_CODE, EMAIL, ADDRESS, " +
                    "APPEAL_SOURCE, POST_CODE, COMPLAIN_TIME, IP_ADDRESS, " +
                    "IS_SYSN, IS_APPEAL, SERVICE_STATUS, IS_CANCEL,ORG_CODE,ORG_NAME,SERIAL_ID,CONCACT_NAME,CONCACT_CARD_TYPE,CONCACT_ID_CARD,EXIST_ATTACHMENT" +
                    ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?,?,?,?)" +
                    " ON DUPLICATE KEY UPDATE " +
                    "M_ID = VALUES(M_ID), ENT_ID = VALUES(ENT_ID), " +
                    "BUSI_ORDER_ID = VALUES(BUSI_ORDER_ID), CREATE_TIME = VALUES(CREATE_TIME), " +
                    "ENT_TYPE = VALUES(ENT_TYPE), ENT_DEPT_CODE = VALUES(ENT_DEPT_CODE), " +
                    "ENT_DEPT_NAME = VALUES(ENT_DEPT_NAME), PROVINCE_CODE = VALUES(PROVINCE_CODE), " +
                    "PROVINCE_NAME = VALUES(PROVINCE_NAME), CITY_CODE = VALUES(CITY_CODE), " +
                    "CITY_NAME = VALUES(CITY_NAME), IS_COMPLAINED = VALUES(IS_COMPLAINED), " +
                    "IS_BACK_RESULT = VALUES(IS_BACK_RESULT), IS_SATISFY_RESULT = VALUES(IS_SATISFY_RESULT), " +
                    "APPEAL_TIME = VALUES(APPEAL_TIME), APPEAL_NAME = VALUES(APPEAL_NAME), " +
                    "CARD_TYPE = VALUES(CARD_TYPE), ID_CARD = VALUES(ID_CARD), " +
                    "PHONE = VALUES(PHONE), APPEAL_PHONE = VALUES(APPEAL_PHONE), " +
                    "APPEAL_CONTENT = VALUES(APPEAL_CONTENT), APPEAL_ATTACHMENT = VALUES(APPEAL_ATTACHMENT), " +
                    "APPEAL_CODE = VALUES(APPEAL_CODE), EMAIL = VALUES(EMAIL), " +
                    "ADDRESS = VALUES(ADDRESS), APPEAL_SOURCE = VALUES(APPEAL_SOURCE), " +
                    "POST_CODE = VALUES(POST_CODE), COMPLAIN_TIME = VALUES(COMPLAIN_TIME), " +
                    "IP_ADDRESS = VALUES(IP_ADDRESS), IS_SYSN = VALUES(IS_SYSN), " +
                    "IS_APPEAL = VALUES(IS_APPEAL), SERVICE_STATUS = VALUES(SERVICE_STATUS), " +
                    "IS_CANCEL = VALUES(IS_CANCEL),ORG_CODE = VALUES(ORG_CODE),ORG_NAME = VALUES(ORG_NAME),SERIAL_ID = VALUES(SERIAL_ID),CONCACT_NAME = VALUES(CONCACT_NAME),CONCACT_CARD_TYPE = VALUES(CONCACT_CARD_TYPE),CONCACT_ID_CARD = VALUES(CONCACT_ID_CARD),EXIST_ATTACHMENT = VALUES(EXIST_ATTACHMENT)";


            
            // 准备批量处理
            List<Object[]> batchParams = new ArrayList<>();
            Map<String, JSONObject> orderMap = new HashMap<>(); // 用于记录工单ID和对应的工单数据
            
            // 预处理数据
            for (int i = 0; i < dataList.size(); i++) {
                JSONObject order = dataList.get(i);
                
                try {
                    // 生成工单ID
                    String orderId = StringUtils.isNotBlank(order.getString("id")) ? order.getString("id") : UUID.randomUUID().toString().replace("-", "");
                    
                    // 准备插入参数
                    Object[] params = new Object[41];
                    params[0] = orderId;
                    params[1] = order.getString("mId");
                    params[2] = order.getString("entId");
                    params[3] = order.getString("busiOrderId");
                    params[4] = StringUtils.isNotBlank(order.getString("createTime")) ? order.getString("createTime") : DateUtil.getCurrentDateStr();
                    params[5] = order.getString("entType");
                    params[6] = order.getString("entDeptCode");
                    params[7] = order.getString("entDeptName");
                    params[8] = order.getString("provinceCode");
                    params[9] = order.getString("provinceName");
                    params[10] = order.getString("cityCode");
                    params[11] = order.getString("cityName");
                    params[12] = order.getString("isComplained");
                    params[13] = order.getString("isBackResult");
                    params[14] = order.getString("isSatisfyResult");
                    params[15] = order.getString("appealTime");
                    params[16] = order.getString("appealName");
                    params[17] = order.getString("cardType");
                    params[18] = order.getString("idCard");
                    params[19] = order.getString("phone");
                    params[20] = order.getString("appealPhone");
                    params[21] = order.getString("appealContent");
                    params[22] = order.getString("appealAttachment");
                    params[23] = order.getString("appealCode");
                    params[24] = order.getString("email");
                    params[25] = order.getString("address");
                    params[26] = order.getString("appealSource");
                    params[27] = order.getString("postCode");
                    params[28] = order.getString("complainTime");
                    params[29] = order.getString("ipAddress");
                    params[30] = "N"; // 初始标记为未同步
                    params[31] = order.getString("isAppeal");
                    params[32] = order.getString("serviceStatus");
                    params[33] = order.getString("isCancel");
                    params[34] = order.getString("orgCode");
                    params[35] = order.getString("orgName");
                    params[36] = order.getString("serialId");
                    params[37] = order.getString("concactName");
                    params[38] = order.getString("concactCardType");
                    params[39] = order.getString("concactIdCard");
                    params[40] = order.getString("existAttachment");
                    // 添加到批处理列表
                    batchParams.add(params);
                    orderMap.put(orderId, order);
                    
                    // 达到批处理大小时执行批量插入
                    if (batchParams.size() >= batchSize) {
                        executeBatchInsert(query, insertSql, batchParams, orderMap, savedOrderIds, failedOrders);
                        batchParams.clear();
                        orderMap.clear();
                    }
                } catch (Exception e) {
                    logger.error("准备工单数据失败，工单ID: {}, 错误: {}", order.getString("id"), e.getMessage(), e);
                    failedOrders.add(order);
                }
            }
            
            // 处理剩余的批次
            if (!batchParams.isEmpty()) {
                executeBatchInsert(query, insertSql, batchParams, orderMap, savedOrderIds, failedOrders);
            }
            
            // 回推更新同步状态
            if (!savedOrderIds.isEmpty()) {
                updateSyncStatusToSource(savedOrderIds);
            }
            
            logger.info("成功保存{}条工单数据到数据库，失败{}条", savedOrderIds.size(), failedOrders.size());
            
        } catch (Exception e) {
            logger.error("保存工单数据到数据库失败: " + e.getMessage(), e);
            throw new RuntimeException("保存工单数据到数据库失败", e);
        }
    }
    
    /**
     * 执行批量插入
     * @param query 查询对象
     * @param insertSql 插入SQL
     * @param batchParams 批量参数
     * @param orderMap 工单映射
     * @param savedOrderIds 保存成功的工单ID列表
     * @param failedOrders 保存失败的工单列表
     */
    private void executeBatchInsert(EasyQuery query, String insertSql, List<Object[]> batchParams, 
                                   Map<String, JSONObject> orderMap, List<String> savedOrderIds, List<JSONObject> failedOrders) {
        try {
            // 执行批量插入
            query.executeBatch(insertSql, batchParams);
            
            // 处理附件和记录成功ID
            for (Object[] params : batchParams) {
                String orderId = (String) params[0];
                JSONObject order = orderMap.get(orderId);
                
                // 处理附件上传
                String appealAttachment = order.getString("appealAttachment");
                //EXIST_ATTACHMENT
                String existAttachment = order.getString("existAttachment");;
                if (StringUtils.isNotBlank(appealAttachment) && "01".equals(existAttachment)) {
                    try {
                        processAttachments(orderId, appealAttachment, order.getString("entId"), order.getString("busiOrderId"));
                    } catch (Exception e) {
                        logger.error("处理附件失败，工单ID: {}, 错误: {}", orderId, e.getMessage(), e);
                    }
                }
                
                // 记录成功插入的工单ID
                savedOrderIds.add(orderId);
            }
            
            logger.info("批量保存{}条工单数据成功", batchParams.size());
        } catch (Exception e) {
            logger.error("批量保存工单数据失败: {}", e.getMessage(), e);
            
            // 批量失败时，尝试单条插入
            logger.info("尝试单条插入模式");
            for (Object[] params : batchParams) {
                String orderId = (String) params[0];
                JSONObject order = orderMap.get(orderId);
                
                try {
                    query.execute(insertSql, params);
                    
                    // 处理附件上传
                    String appealAttachment = order.getString("appealAttachment");
                    if (StringUtils.isNotBlank(appealAttachment)) {
                        processAttachments(orderId, appealAttachment, order.getString("entId"), order.getString("busiOrderId"));
                    }
                    
                    // 记录成功插入的工单ID
                    savedOrderIds.add(orderId);
                    
                    logger.info("单条保存工单数据成功，工单ID: {}", orderId);
                } catch (Exception ex) {
                    logger.error("单条保存工单数据失败，工单ID: {}, 错误: {}", orderId, ex.getMessage(), ex);
                    failedOrders.add(order);
                }
            }
        }
    }
    
    /**
     * 处理附件上传到S3
     * @param orderId 工单ID
     * @param appealAttachment 附件信息
     * @param entId 企业ID
     * @param busiOrderId 业务工单ID
     */
    private void processAttachments(String orderId, String appealAttachment, String entId, String busiOrderId) {
        try {
            // 通过接口获取附件列表
            JSONObject attachmentResult = getAttachmentListFromInterface(appealAttachment);
            
            if (attachmentResult != null && attachmentResult.getBooleanValue("success")) {
                JSONArray attachments = attachmentResult.getJSONArray("data");
                
                if (attachments != null && !attachments.isEmpty()) {
                    for (int i = 0; i < attachments.size(); i++) {
                        JSONObject attachment = attachments.getJSONObject(i);
                        try {
                            // 通过getFile接口获取文件并上传到S3
                            downloadAndUploadAttachment(attachment, appealAttachment, entId, busiOrderId);
                        } catch (Exception e) {
                            logger.error("上传附件到S3失败，附件ID: {}, 错误: {}", attachment.getString("id"), e.getMessage(), e);
                        }
                    }
                }
            } else {
                logger.warn("获取附件列表失败或无附件数据，工单ID: {}", orderId);
            }
        } catch (Exception e) {
            logger.error("处理附件失败，工单ID: {}, 错误: {}", orderId, e.getMessage(), e);
        }
    }
    
    /**
     * 通过接口获取附件列表
     * @param busiId 业务ID
     * @return 附件列表结果
     */
    private JSONObject getAttachmentListFromInterface(String busiId) {
        try {
            // 构建请求参数
            JSONObject requestData = new JSONObject();
            requestData.put("command", "getFileList");
            requestData.put("busiId", busiId);
            requestData.put("appId", Constants.getAppId());
            
            // 添加时间戳
            String timestamp = String.valueOf(System.currentTimeMillis());
            requestData.put("timestamp", timestamp);
            
            // 计算签名
            String secretKey = Constants.getSysSecretKey();
            String signature = calculateSignature(requestData, secretKey);
            requestData.put("signature", signature);
            
            logger.info("发送获取附件列表请求: {}", requestData.toJSONString());
            
            // 发送HTTP请求
            String response = OkHttpUtil.getInstance().postSync(getOutInterfaceUrl(), requestData.toJSONString(), "application/json");
            
            if (StringUtils.isNotBlank(response)) {
                JSONObject result = JSONObject.parseObject(response);
                logger.info("收到附件列表响应: {}", result.toJSONString());
                return result;
            } else {
                logger.error("获取附件列表返回空响应");
                return createErrorResult("获取附件列表返回空响应");
            }
            
        } catch (Exception e) {
            logger.error("获取附件列表失败: " + e.getMessage(), e);
            return createErrorResult("获取附件列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 下载附件并上传到S3
     * @param attachment 附件信息
     * @param busiId 工单ID
     * @param entId 企业ID
     * @param busiOrderId 业务工单ID
     */
    private void downloadAndUploadAttachment(JSONObject attachment, String busiId, String entId, String busiOrderId) {
        InputStream is = null;
        try {
            String fileName = attachment.getString("fileName");
            String attachmentId = attachment.getString("id");
            
            if (StringUtils.isBlank(fileName) || StringUtils.isBlank(attachmentId)) {
                logger.warn("附件文件名或ID为空，跳过上传，附件ID: {}", attachmentId);
                return;
            }
            
            // 通过getFile接口获取文件流
            is = getFileStreamFromInterface(attachmentId);
            if (is == null) {
                logger.warn("无法获取附件文件流，跳过上传，附件ID: {}", attachmentId);
                return;
            }
            
            // 获取文件大小（这里可能需要从attachment信息中获取，或者通过其他方式）
            long fileSize = attachment.getLongValue("fileSize");
            if (fileSize <= 0) {
                // 如果无法获取文件大小，可以尝试读取流来计算，但这里简化处理
                fileSize = 1024; // 默认值
            }
            
            // 校验附件：检查后缀、文件大小
            String checkResult = OSSAttachmentUtil.checkSizeAndSuffix(fileName, fileSize);
            if (StringUtils.isNotBlank(checkResult)) {
                logger.error("上传附件[{},{}]失败: {}", fileName, fileSize, checkResult);
                return;
            }
            
            AttachmentFile afile = new AttachmentFile(null, is, "appeal", fileName);
            afile.setFileSize(fileSize);
            afile.setBusiId(busiId);
            afile.setDelUploadOldFile(true);
            
            // 上传文件到S3
            OSSTools.getInstance().uploadFile(afile);
            
            // 准备入库
            AttachmentDbinfo attachmentDbinfo = new AttachmentDbinfo(afile, QueryFactory.getQuery(), Constants.getSysSchema(), entId, busiOrderId);
            attachmentDbinfo.setUserAcc("system");
            attachmentDbinfo.setUserName("system");
            
            // 数据入库
            boolean flag = AttachmentUtil.saveAttachment(attachmentDbinfo);
            if (!flag) {
                logger.error("上传附件失败，附件入库保存失败: {}", fileName);
            } else {
                logger.info("成功上传附件到S3: {}", fileName);
            }
            
        } catch (Exception e) {
            logger.error("下载并上传附件时异常: {}", e.getMessage(), e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    logger.error("关闭文件流时异常: {}", e.getMessage(), e);
                }
            }
        }
    }
    
    /**
     * 通过getFile接口获取文件流
     * @param attachmentId 附件ID
     * @return 文件输入流
     */
    private InputStream getFileStreamFromInterface(String attachmentId) {
        try {
            // 构建请求参数
            JSONObject requestData = new JSONObject();
            requestData.put("command", "getFile");
            requestData.put("id", attachmentId);
            requestData.put("appId", Constants.getAppId());
            
            // 添加时间戳
            String timestamp = String.valueOf(System.currentTimeMillis());
            requestData.put("timestamp", timestamp);
            
            // 计算签名
            String secretKey = Constants.getSysSecretKey();
            String signature = calculateSignature(requestData, secretKey);
            requestData.put("signature", signature);
            
            logger.info("发送获取文件请求: {}", requestData.toJSONString());
            
            return OkHttpUtil.getInstance().postSyncForStream(getOutInterfaceUrl(), requestData.toJSONString(), "application/json");
            
        } catch (Exception e) {
            logger.error("获取文件流异常: {}", e.getMessage(), e);
            return null;
        }
    }
    

    
    /**
     * 回推更新源系统的同步状态
     * @param orderIds 工单ID列表
     */
    private void updateSyncStatusToSource(List<String> orderIds) {
        try {
            // 构建请求参数
            JSONObject requestData = new JSONObject();
            requestData.put("command", "updateSyncStatus");
            requestData.put("orderIds", StringUtils.join(orderIds, ","));
            requestData.put("timestamp", System.currentTimeMillis());
            requestData.put("appId", Constants.getAppId());
            // 计算签名
            String secretKey = Constants.getSysSecretKey();
            String signature = calculateSignature(requestData, secretKey);
            requestData.put("signature", signature);
            logger.info("发送回推更新同步状态请求: {}", requestData.toJSONString());
            // 发送HTTP请求
            String respStr = OkHttpUtil.getInstance().postSync(getOutInterfaceUrl(), requestData.toJSONString(), "application/json");;
            if (StringUtils.isNotBlank(respStr)) {
                JSONObject result = JSONObject.parseObject(respStr);
                if (result.getBooleanValue("success")) {
                    logger.info("成功回推更新{}条工单的同步状态", orderIds.size());
                } else {
                    logger.error("回推更新同步状态失败: {}", result.getString("message"));
                }
            }
            
        } catch (Exception e) {
            logger.error("回推更新同步状态异常: {}", e.getMessage(), e);
        }
    }
    
}