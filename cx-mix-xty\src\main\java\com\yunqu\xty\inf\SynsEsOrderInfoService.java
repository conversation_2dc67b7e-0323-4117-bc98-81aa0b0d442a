package com.yunqu.xty.inf;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.IBaseService;
import com.yunqu.xty.base.Constants;
import com.yunqu.xty.excuotr.EventDispatcher;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;

/**
 * 同步es订单信息
 */
public class SynsEsOrderInfoService extends IBaseService{


	@Override
	public String getServiceId() {
		return "XTY_ORDER_SYN_ES_SERVICE";
	}

	@Override
	public String getName() {
		return Constants.APP_NAME;
	}

	@Override
	public JSONObject invokeMethod(JSONObject param) throws ServiceException {
		String orderId = param.getString("orderId");
		String schema = param.getString("schema");
		//执行同步ES操作
        EventDispatcher.appendAddDocEvent(orderId, schema, "SynsEsOrderInfoService");
		return EasyResult.ok();
	}


}
