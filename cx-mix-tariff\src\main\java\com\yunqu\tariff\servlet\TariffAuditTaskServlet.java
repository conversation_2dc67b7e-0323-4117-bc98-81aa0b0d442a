package com.yunqu.tariff.servlet;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.slf4j.Logger;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.tariff.base.AppBaseServlet;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.service.TariffAuditTaskService;
import com.yunqu.tariff.utils.EnhancedZipUtil;
import com.yunqu.tariff.utils.ZipFileValidator;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.servlet.ServletUtil;

/**
 * 稽核任务控制器
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
@WebServlet("/servlet/tariffAuditTask/*")
@MultipartConfig(maxFileSize = 50 * 1024 * 1024) // 50MB
public class TariffAuditTaskServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;
    private static final Logger logger = CommonLogger.getLogger("audit-servlet");

    /**
     * 创建稽核任务
     */
    public JSONObject actionForAdd() {
        try {
            // 获取表单数据
            JSONObject taskData = this.getJSONObject();

            // 验证必填参数
            String taskName = taskData.getString("taskName");
            String provinceCode = taskData.getString("provinceCode");
            String provinceName = taskData.getString("provinceName");
            String entCode = taskData.getString("entCode");
            String entName = taskData.getString("entName");
            String salesFileName = taskData.getString("salesFileName");
            String ordersFileName = taskData.getString("ordersFileName");
            String productIdColumn = taskData.getString("productIdColumn");
            String productNameColumn = taskData.getString("productNameColumn");
            String productCodeColumn = taskData.getString("productCodeColumn");
            String filePath = taskData.getString("filePath");
            String zipFileName = taskData.getString("zipFileName");

            if (StringUtils.isBlank(taskName)) {
                return EasyResult.error(400, "任务名称不能为空");
            }
            if (StringUtils.isBlank(provinceCode)) {
                return EasyResult.error(400, "省份编码不能为空");
            }
            if (StringUtils.isBlank(entCode)) {
                return EasyResult.error(400, "运营商编码不能为空");
            }

            // 获取用户信息
            String entId = getEntId();
            String busiOrderId = getBusiOrderId();
            String userAcc = getUserAcct();
            String userName = UserUtil.getUser(getRequest()).getUserName();

            String uploadDir = appContext.getProperty("SFTP_LOCAL_BASE_PATH", "") + File.separator;
            File zipFile = new File(uploadDir + filePath);

            // 创建任务（暂时不处理文件上传，后续在actionForUpload中处理）

            return TariffAuditTaskService.createTask(taskData, zipFile, entId, busiOrderId, userAcc, userName);

        } catch (Exception e) {
            logger.error("创建稽核任务失败", e);
            return EasyResult.error(500, "系统异常: " + e.getMessage());
        }
    }

    /**
     * 上传ZIP文件并解析文件列表
     */
    public JSONObject actionForUploadFile() {
        try {
            // 检查是否为multipart请求
            String contentType = this.getRequest().getContentType();
            if (contentType == null || !contentType.toLowerCase().contains("multipart/form-data")) {
                return EasyResult.error(400, "请求格式错误，需要multipart/form-data");
            }

            // 获取上传的文件
            Part filePart = this.getFile("file");

            if (filePart == null) {
                return EasyResult.error(400, "未找到上传文件");
            }

            // 获取文件名
            String fileName = getFileName(filePart);
            if (fileName == null || !fileName.toLowerCase().endsWith(".zip")) {
                return EasyResult.error(400, "只支持ZIP格式文件");
            }
            String extName = FileUtil.extName(fileName);

            // 检查文件大小（50MB限制）
            long fileSize = filePart.getSize();
            if (fileSize > 50 * 1024 * 1024) {
                return EasyResult.error(400, "文件大小不能超过50MB");
            }

            // 创建上传目录
            String uploadDir = appContext.getProperty("SFTP_LOCAL_BASE_PATH", "") + File.separator;
            File uploadDirFile = new File(uploadDir);
            if (!uploadDirFile.exists()) {
                uploadDirFile.mkdirs();
            }

            String uuid = IdUtil.getSnowflakeNextIdStr();
            // 生成唯一文件名
            String uniqueFileName = IdUtil.getSnowflakeNextIdStr() + "." + extName;
            String localFilePath = "audit" + File.separator + uuid + File.separator + uniqueFileName;
            String filePath = uploadDir + localFilePath;
            String filePathForlder = uploadDir + "audit" + File.separator + uuid;
            File folder = new File(filePathForlder);
            if (!folder.exists()) {
                folder.mkdirs();
            }

            // 保存文件
            try (InputStream inputStream = filePart.getInputStream();
                    FileOutputStream outputStream = new FileOutputStream(filePath)) {

                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }

            // 验证并解析ZIP文件中的文件列表
            ZipFileValidator.ValidationResult validation = ZipFileValidator.validateZipFile(filePath);
            if (!validation.isValid()) {
                // 删除上传的文件
                new File(filePath).delete();
                return EasyResult.error(400, "ZIP文件格式错误: " + validation.getErrorMessage());
            }

            List<String> allFiles = EnhancedZipUtil.parseZipFileListSafely(filePath);

            // 过滤出Excel文件
            List<String> excelFiles = new ArrayList<>();
            for (String file : allFiles) {
                if (file.toLowerCase().endsWith(".xlsx") || file.toLowerCase().endsWith(".xls")) {
                    excelFiles.add(file);
                }
            }

            if (excelFiles.isEmpty()) {
                // 删除上传的文件
                new File(filePath).delete();
                return EasyResult.error(400, "ZIP文件中未找到Excel文件");
            }

            // 构建返回结果
            JSONObject result = new JSONObject();
            result.put("fileName", fileName);
            result.put("filePath", localFilePath);
            result.put("fileSize", fileSize);
            result.put("fileList", excelFiles);
            result.put("fileCount", excelFiles.size());

            logger.info("文件上传成功: {}, 包含Excel文件: {}", fileName, excelFiles);
            return EasyResult.ok(result);

        } catch (Exception e) {
            logger.error("上传文件失败", e);
            return EasyResult.error(500, "上传文件失败：" + e.getMessage());
        }
    }

    /**
     * 获取Excel文件的列名
     */
    public JSONObject actionForGetExcelColumns() {
        try {
            String body = ServletUtil.getBody(getRequest());
            JSONObject params = JSONObject.parseObject(body);
            String filePath = params.getString("filePath");
            String productFileName = params.getString("productFileName");
            String orderFileName = params.getString("orderFileName");
            String uploadDir = appContext.getProperty("SFTP_LOCAL_BASE_PATH", "") + File.separator;
            filePath = uploadDir + filePath;

            // 解析Excel文件的列名
            List<String> productColumns = parseExcelColumns(filePath, productFileName);
            List<String> orderColumns = parseExcelColumns(filePath, orderFileName);
            logger.info("为文件 {} 解析到列名: {}", productFileName, productColumns);
            logger.info("为文件 {} 解析到列名: {}", orderFileName, orderColumns);

            Map<String, List<String>> columns = new HashMap<>();
            columns.put("product", productColumns);
            columns.put("order", orderColumns);

            return EasyResult.ok(columns);

        } catch (Exception e) {
            logger.error("获取Excel列名失败", e);
            return EasyResult.error(500, "获取Excel列名失败：" + e.getMessage());
        }
    }

    /**
     * 从Part中获取文件名
     */
    private String getFileName(Part part) {
        String contentDisposition = part.getHeader("content-disposition");
        if (contentDisposition != null) {
            for (String content : contentDisposition.split(";")) {
                if (content.trim().startsWith("filename")) {
                    String fileName = content.substring(content.indexOf('=') + 1).trim().replace("\"", "");
                    return fileName;
                }
            }
        }
        return null;
    }

    /**
     * 解析ZIP文件中的文件列表（已弃用，使用EnhancedZipUtil替代）
     * 
     * @deprecated 使用 {@link EnhancedZipUtil#parseZipFileListSafely(String)} 替代
     */
    @Deprecated
    private List<String> parseZipFileList(String zipFilePath) throws IOException {
        logger.warn("使用已弃用的parseZipFileList方法，建议使用EnhancedZipUtil.parseZipFileListSafely");
        return EnhancedZipUtil.parseZipFileListSafely(zipFilePath);
    }

    /**
     * 解析Excel文件的列名
     */
    private List<String> parseExcelColumns(String zipFilePath, String excelFileName) throws IOException {
        List<String> columns = new ArrayList<>();

        try {
            // 使用增强的ZIP工具获取文件输入流
            InputStream inputStream = EnhancedZipUtil.getFileInputStreamSafely(zipFilePath, excelFileName);
            if (inputStream == null) {
                logger.error("在ZIP文件中未找到Excel文件: {}", excelFileName);
                return columns;
            }

            try {
                // 解析Excel列名
                columns = parseExcelColumnsFromStream(inputStream, excelFileName);
            } finally {
                // 确保关闭输入流
                EnhancedZipUtil.closeStreamSafely(inputStream);
            }

        } catch (Exception e) {
            logger.error("解析Excel文件列名失败: zipFile={}, excelFile={}", zipFilePath, excelFileName, e);
            throw new IOException("解析Excel文件列名失败: " + e.getMessage(), e);
        }

        return columns;
    }

    /**
     * 从输入流解析Excel列名
     */
    private List<String> parseExcelColumnsFromStream(InputStream inputStream, String fileName) throws IOException {
        List<String> columns = new ArrayList<>();

        // 创建临时文件
        File tempFile = File.createTempFile("excel_", fileName.substring(fileName.lastIndexOf(".")));
        try {
            // 将流内容写入临时文件
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[1024];
                int length;
                while ((length = inputStream.read(buffer)) > 0) {
                    fos.write(buffer, 0, length);
                }
            }

            // 解析Excel文件
            try (FileInputStream fis = new FileInputStream(tempFile)) {
                Workbook workbook;
                if (fileName.toLowerCase().endsWith(".xlsx")) {
                    workbook = new XSSFWorkbook(fis);
                } else {
                    workbook = new HSSFWorkbook(fis);
                }

                Sheet sheet = workbook.getSheetAt(0); // 读取第一个工作表
                Row headerRow = sheet.getRow(0); // 读取第一行作为表头

                if (headerRow != null) {
                    for (Cell cell : headerRow) {
                        String cellValue = getCellValueAsString(cell);
                        if (cellValue != null && !cellValue.trim().isEmpty()) {
                            columns.add(cellValue.trim());
                        }
                    }
                }

                workbook.close();
            }

        } finally {
            // 删除临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }

        return columns;
    }

    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }
        return cell.getStringCellValue();
    }

    /**
     * 下载文件
     */
    public void actionForDownload() {
        String taskId = getPara("id");
        String type = getPara("type");
        logger.info("下载文件 id:" + taskId + " type:" + type);
        try {
            JSONObject task = QueryFactory.getTariffQuery().queryForRow(
                    "select * from " + getTableName("xty_tariff_audit_task") + " where id=?", new Object[] { taskId },
                    new JSONMapperImpl());
            String fileName = "";
            String filePath = "";
            if (StringUtils.equals("unpublic", type)) {
                fileName = task.getString("UNPUBLIC_FILE_NAME");
                filePath = task.getString("UNPUBLIC_FILE_PATH");
            } else if (StringUtils.equals("unreport", type)) {
                fileName = task.getString("UNREPORT_FILE_NAME");
                filePath = task.getString("UNREPORT_FILE_PATH");
            } else if (StringUtils.equals("sftp", type)) {
                fileName = task.getString("SFTP_REMOTE_FILE_NAME");
                filePath = task.getString("SFTP_REMOTE_FILE_PATH");
            } else if (StringUtils.equals("zip", type)) {
                fileName = task.getString("REAL_ZIP_FILE_NAME");
                filePath = task.getString("ZIP_FILE_PATH");
            }
            if (StringUtils.isBlank(fileName) || StringUtils.isBlank(filePath)) {
                Render.renderJson(getRequest(), getResponse(), EasyResult.fail("未发现导出任务文件"));
                return;
            }
            Render.renderFile(getRequest(), getResponse(), new File(filePath), fileName, false);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            Render.renderJson(getRequest(), getResponse(), EasyResult.fail("系统异常，请联系管理员"));
        }
    }

}