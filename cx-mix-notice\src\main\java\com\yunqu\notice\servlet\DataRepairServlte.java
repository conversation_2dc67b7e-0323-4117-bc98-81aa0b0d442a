package com.yunqu.notice.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.PhoneCryptor;
import com.yunqu.notice.base.CommonLogger;
import com.yunqu.notice.base.AppBaseServlet;
import com.yunqu.notice.base.QueryFactory;
import lombok.extern.flogger.Flogger;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.openxmlformats.schemas.drawingml.x2006.main.impl.CTConnectionSiteImpl;

import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@WebServlet("/servlet/repair")
public class DataRepairServlte extends AppBaseServlet {


    public JSONObject actionForRepairDataSh () {
        String[] arr =  new String[]{"部转-20250600340470"};
        EasyQuery query = getQuery();
        for (String s : arr) {
            try {
                JSONObject data = getData(s);
                CommonLogger.logger.info("处理条数：" + arr.length);
                if (Objects.nonNull(data)) {
                    String mId = data.getString("M_ID");
                    JSONObject task = getTask(mId, "USERTASK_4p8o7zinvq");
                    String entId = getEntId();
                    String busiOrderId = getBusiOrderId();
                    String completeTime = task.getString("COMPLETE_TIME");
                    String currentDateStr = DateUtil.getCurrentDateStr();
                    String handle_acc = task.getString("HANDLE_ACC");
                    String handleName = task.getString("HANDLE_NAME");
                    //INSERT INTO ycbusi_ekf.c_sms_info
                    //(ID, `TYPE`, SOURCE, BUSI_ID, CONTENT, SEND_TIME, CHANNEL_ID, STATUS, NEED_SMS_RECEIPT, CREATE_ACC, CREATE_NO, CREATE_USER_NAME, CREATE_DEPT, EP_CODE, CREATE_TIME, EX_JSON, BUSI_ORDER_ID, EX1, SUCCEED_COUNT, FAIL_COUNT, TOTAL)
                    EasyRecord record = new EasyRecord(getTableName("c_sms_info"),"ID");
                    String id = "repsh_" + IDGenerator.getDefaultNUMID();
                    record.put("ID", id);
                    record.put("TYPE","1");
                    record.put("SOURCE","01");
                    record.put("BUSI_ID","007");
                    record.put("CONTENT","【"+ task.getString("DEPT_NAME") +"】"+data.getString("AUDIT_SMS_CONTENT"));
                    record.put("SEND_TIME", currentDateStr);
                    record.put("CHANNEL_ID","6919904469289848");
                    record.put("STATUS","1");
                    record.put("NEED_SMS_RECEIPT","1");
                    record.put("CREATE_ACC", handle_acc);
                    //record.put("CREATE_NO", handle_acc);
                    record.put("CREATE_USER_NAME", handleName);
                    record.put("CREATE_DEPT",task.getString("DEPT_CODE"));
                    record.put("EP_CODE",entId);
                    record.put("CREATE_TIME", completeTime);
                    record.put("BUSI_ORDER_ID",busiOrderId);
                    record.put("EX1","sssh");
                    record.put("SUCCEED_COUNT","0");
                    record.put("FAIL_COUNT","0");
                    record.put("TOTAL","1");
                    CommonLogger.logger.info("插入c_sms_info记录：" + JSONObject.toJSONString(record));
                    query.save(record);

                    //INSERT INTO ycbusi_ekf.c_sms_send_record
                    //(ID, USER_TYPE, USER_ID, SEND_TIME, CONTENT, SEND_TYPE, SENDER, BILL_NUM, RECEIVER, SEND_STATUS, CREATE_TIME, SEND_TIMES, FEE_TYPE, PRIVATE_DATA, SMS_FLAG, SMS_SEND_ID, SMS_INFO_ID, BUSINESS_TYPE, BUSINESS_ID, CHANNEL_ID, NEED_SMS_RECEIPT, SMS_CHANNEL_RECORDID, SEND_BATCH, EP_CODE, SEND_RESULT_DESC, SMS_UPLOAD_ID, EX_JSON, BUSI_ORDER_ID, DATA1, DATA2, DATA3, DATA4, DATA5)
                    //VALUES('0000000804930917', '1', NULL, '2024-04-08 17:26:32', '【四川省电信用户申诉受理中心】用户您好，依据《电信用户申诉处理办法》第11条第2款的规定，申诉人与被申诉人已经达成和解协议并执行的，申诉受理机构不予受理。据被申诉人反馈您与其已经达成和解协议并执行，本次申诉我中心不予受理。如您认为被申诉人反馈情况不实，请您重新向我中心提交申诉材料。', '1', NULL, NULL, '***********', '3', '2024-04-08 17:16:32', 1, NULL, NULL, NULL, NULL, '2479364568636387', '1', '17120104322040288651088', '6919904469289848', '1', NULL, 0, '1000', '处理成功', NULL, '{"smsRecordId":"0000000804930917"}', '83880897233159997654981', NULL, NULL, NULL, NULL, NULL);
                    // 创建一个新的EasyRecord对象，指定表名和主键字段
                    EasyRecord sms = new EasyRecord(getTableName("c_sms_send_record"), "ID");
                    String numid = IDGenerator.getDefaultNUMID();
                    sms.put("ID", IDGenerator.getDefaultNUMID());
                    sms.put("USER_TYPE", "1");
                    sms.put("USER_ID", null); // 注意：如果字段可以为NULL，则直接传入null
                    sms.put("SEND_TIME", currentDateStr);
                    sms.put("CONTENT", "【"+ task.getString("DEPT_NAME") +"】"+data.getString("AUDIT_SMS_CONTENT"));
                    sms.put("SEND_TYPE", "1");
                    sms.put("SENDER", null); // 同上，如果字段可以为NULL
                    sms.put("BILL_NUM", null); // 同上
                    sms.put("RECEIVER", PhoneCryptor.getInstance().decrypt(data.getString("PHONE")));
                    sms.put("SEND_STATUS", "4");
                    sms.put("CREATE_TIME", completeTime);
                    sms.put("SEND_TIMES", 4); // 注意：数字类型直接传入数字，不需要引号
                    sms.put("FEE_TYPE", null); // 同上
                    sms.put("PRIVATE_DATA", null); // 同上
                    sms.put("SMS_FLAG", null); // 同上
                    sms.put("SMS_SEND_ID", null); // 同上
                    sms.put("SMS_INFO_ID", id);
                    sms.put("BUSINESS_TYPE", "1");
                    sms.put("BUSINESS_ID", mId);
                    sms.put("CHANNEL_ID", "6919904469289848");
                    sms.put("NEED_SMS_RECEIPT", "1");
                    sms.put("SMS_CHANNEL_RECORDID", null); // 同上
                    sms.put("SEND_BATCH", 0); // 数字类型
                    sms.put("EP_CODE", entId);
                    sms.put("SEND_RESULT_DESC", "");
                    sms.put("SMS_UPLOAD_ID", null); // 同上
                    JSONObject ex = new JSONObject();
                    ex.put("smsRecordId",numid);
                    sms.put("EX_JSON", JSONObject.toJSONString(ex)); // JSON字符串直接传入
                    sms.put("BUSI_ORDER_ID", busiOrderId);
                    sms.put("DATA1", null); // 同上，为其他可能的字段设置值，如果不需要可以省略或设置为null
                    sms.put("DATA2", null); // 同上
                    sms.put("DATA3", null); // 同上
                    sms.put("DATA4", null); // 同上
                    sms.put("DATA5", null); // 同上
                    CommonLogger.logger.info("c_sms_send_record：" + JSONObject.toJSONString(sms));
                    query.save(sms);
                }

            } catch (Exception e) {
                CommonLogger.logger.error(e.getMessage(),e);
            }

        }
        return EasyResult.ok();
    }


    private JSONObject getData (String orderNo) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append(" select t1.* from " + getTableName("c_box_appeal_order") + " t1 ");
        sql.append(" left join " + getTableName("c_bo_base_order") + " t2 on t2.ID = t1.M_ID ");
        sql.append(orderNo," where t2.ORDER_NO = ? ",false);
        CommonLogger.logger.info("sql:" +sql.getFullSq());
        return getQuery().queryForRow(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
    }

    private JSONObject getMediateData (String orderNo) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append(" select t1.*,t2.PHONE from " + getTableName("c_box_mediate_order") + " t1 ");
        sql.append(" left join " + getTableName("c_box_appeal_order") + " t2 on t2.M_ID = t1.P_ID ");
        sql.append(orderNo," where t1.P_ORDER_NO = ? ",false);
        CommonLogger.logger.info("sql:" +sql.getFullSq());
        return getQuery().queryForRow(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
    }

    private JSONObject getTask(String mId,String nodeId) throws SQLException {
        EasySQL sql =new EasySQL();
        sql.append(" select * from " + getTableName("xty_ru_task_his"));
        sql.append(" where 1=1 ");
        sql.append(mId," and M_ID= ? ",false);
        sql.append(nodeId," and NODE_ID = ? ",false);
        sql.append("01" ," and IS_COMPLETE=? ");
        return getQuery().queryForRow(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
    }


    public JSONObject actionForRestartAppeal () throws SQLException {
        EasyQuery query = QueryFactory.getWriteQuery();
        String orderId = getPara("orderId");
        String startTime =   "2025-07-16 00:00:00";
        EasySQL sql = new EasySQL();
        //select t2.* from ycbusi_ekf.c_box_mediate_order t1 left join ycbusi_ekf.c_bo_base_order t2 on t2.ID = t1.M_ID  where t1.CREATE_TIME >='2025-06-24 08:41:42'
        sql.append("select t1.*  from ycbusi_ekf.c_box_appeal_order t1 left join ycbusi_ekf.c_bo_base_order t2 on t2.ID = t1.M_ID  ");
        sql.append(startTime,"where t1.CREATE_TIME >= ?");
        sql.append(orderId," and t2.ID = ?",false);
        sql.append(" and (t2.PROC_KEY is null or t1.SERVICE_STATUS is null) ");
        CommonLogger.getLogger().info(sql.getFullSq());
        List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(jsonObject -> {
                try {
                    String id = jsonObject.getString("M_ID");
                    JSONObject baseJson = this.getQuery().queryForRow("select ID, ORDER_NO, PROC_INST_ID, PROC_KEY  from " + this.getTableName("C_BO_BASE_ORDER") + " where ID = ?", new Object[]{id}, new JSONMapperImpl());
                    String operType = "edit";
                    String flowKey = "xty_ss";
                    JSONObject data = new JSONObject();
                    JSONObject appealObject = new JSONObject(new LinkedHashMap<>());
                    appealObject.putAll(jsonObject);
                    removeEmptyFields(appealObject);
                    data.put("c_box_appeal_order", appealObject);
                    data.put("c_bo_base_order", baseJson);
                    IService service = ServiceContext.getService("XTY_ORDER_SUBMIT_INF_SERVICE");
                    JSONObject json = new JSONObject();
                    json.put("operType", operType);
                    json.put("orderId", jsonObject.getString("M_ID"));
                    json.put("entId", getEntId());
                    json.put("schema", getDbName());
                    json.put("busiOrderId", getBusiOrderId());
                    json.put("userAcc", jsonObject.getString("CREATE_ACC"));
                    json.put("userName", jsonObject.getString("CREATE_NAME"));
                    json.put("flowKey", flowKey);
                    json.put("cityCode", jsonObject.getString("CITY_CODE"));
                    json.put("provinceCode", jsonObject.getString("PROVINCE_CODE"));
                    json.put("data", data);
                    JSONObject urlParam = new JSONObject();
                    json.put("urlParam", urlParam);
                    CommonLogger.getLogger().info("-->[创建申诉工单请求] param:" + json.toJSONString());
                    JSONObject result = service.invoke(json);
                    CommonLogger.getLogger().info("<--[创建申诉工单返回内容] result:" + result.toJSONString());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }

            });
        }
        return EasyResult.ok();
    }

    public static void removeEmptyFields(JSONObject jsonObject) {
        // 使用entrySet()获取所有的键值对，并遍历
        Iterator<Map.Entry<String, Object>> iterator = jsonObject.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = iterator.next();
            Object value = entry.getValue();
            // 检查值是否为空，这里以null和空字符串为例
            if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
                iterator.remove(); // 移除空值字段
            } else if (value instanceof JSONObject) {
                // 如果值是另一个JSONObject，则递归调用此方法
                removeEmptyFields((JSONObject) value);
            }
            // 对于其他类型的值（如数组、数字等），可以根据需要添加额外的检查
        }
    }

    public JSONObject actionForGetData () throws SQLException {
        List <String> ordernos = new ArrayList<>();
        EasyQuery query = QueryFactory.getWriteQuery();
        EasySQL sql = new EasySQL();
        sql.append(" select t2.SECTION_NO from ycbusi_ekf.c_bo_base_order t1 left join ycbusi_ekf.c_box_appeal_order t2 on t2.M_ID = t1.ID where t1.CREATE_TIME >='2025-04-11 16:00:00' and t2.ORG_CODE ='000000' group by t2.SECTION_NO  having count(t2.SECTION_NO) > 1");
        List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(jsonObject -> {
                String sectionNo = jsonObject.getString("SECTION_NO");
                EasySQL sql1 = new EasySQL();
                sql1.append(sectionNo," select t1.ORDER_NO,t2.SERVICE_STATUS from ycbusi_ekf.c_bo_base_order t1 left join ycbusi_ekf.c_box_appeal_order t2 on t2.M_ID = t1.ID where t1.CREATE_TIME >='2025-04-11 16:00:00' and t2.ORG_CODE ='000000' and t2.SECTION_NO = ? ");
                try {
                    List<JSONObject> list1 = query.queryForList(sql1.getSQL(), sql1.getParams(), new JSONMapperImpl());
                    list1.sort(Comparator.comparing(item -> item.getString("SERVICE_STATUS")));
                    if (list1.size() > 1) {
                        ordernos.add(list1.get(0).getString("ORDER_NO"));
                    }
                } catch (SQLException e) {
                    CommonLogger.getLogger().error(e.getMessage(), e);
                    throw new RuntimeException(e);
                }

            });
        }
        return EasyResult.ok(ordernos);
    }

    public JSONObject actionForRestartMediate () throws SQLException {
        EasyQuery query = QueryFactory.getWriteQuery();
        String orderId = getPara("orderId");
        String startTime =  StringUtils.isNotBlank(getPara("startTime")) ? getPara("startTime") :( DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD) + " 00:00:00");
        EasySQL sql = new EasySQL();
        //select t2.* from ycbusi_ekf.c_box_mediate_order t1 left join ycbusi_ekf.c_bo_base_order t2 on t2.ID = t1.M_ID  where t1.CREATE_TIME >='2025-06-24 08:41:42'
        sql.append("select t2.*,t1.ID MEDIATE_ID from ycbusi_ekf.c_box_mediate_order t1 left join ycbusi_ekf.c_bo_base_order t2 on t2.ID = t1.M_ID  ");
        sql.append(startTime,"where t1.CREATE_TIME >= ?");
        sql.append(orderId," and t2.ID = ?");
        sql.append(" and PROC_INST_ID is null");
        CommonLogger.getLogger().info(sql.getFullSq());
        List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(jsonObject -> {
                try {
                    String operType = "add";
                    String flowKey = "xty_tj";
                    String currentTime = DateUtil.getCurrentDateStr();
                    JSONObject data = new JSONObject();
                    JSONObject mediateJson = new JSONObject(new LinkedHashMap<>());
                    mediateJson.put("ID", jsonObject.getString("MEDIATE_ID"));
                    data.put("c_box_mediate_order", mediateJson);
                    JSONObject base = new JSONObject();
                    base.put("P_ORDER_ID", orderId);
                    data.put("c_bo_base_order", base);
                    IService service = ServiceContext.getService("XTY_ORDER_SUBMIT_INF_SERVICE");
                    JSONObject json = new JSONObject();
                    json.put("operType", operType);
                    json.put("orderId", jsonObject.getString("ID"));
                    json.put("entId", getEntId());
                    json.put("schema", getDbName());
                    json.put("busiOrderId", getBusiOrderId());
                    json.put("userAcc", jsonObject.getString("CREATE_ACC"));
                    json.put("userName", jsonObject.getString("CREATE_NAME"));
                    json.put("flowKey", flowKey);
                    json.put("cityCode", jsonObject.getString("CITY_CODE"));
                    json.put("provinceCode", jsonObject.getString("PROVINCE_CODE"));
                    json.put("data", data);
                    JSONObject urlParam = new JSONObject();
                    json.put("urlParam", urlParam);
                    CommonLogger.getLogger().info("-->[创建调解工单请求] param:" + json.toJSONString());
                    JSONObject result = service.invoke(json);
                    CommonLogger.getLogger().info("<--[创建调解工单返回内容] result:" + result.toJSONString());
                } catch (ServiceException e) {
                    throw new RuntimeException(e);
                }

            });
        }
        return EasyResult.ok();
    }

    //select * from ycbusi_ekf.c_bo_order_follow cbof where TASK_NAME ='发起调解' and  CREATE_TIME >='2025-06-24 00:41:42' and CONTENT -> '$.c_box_mediate_order.MEDIATE_NODE' is null
    public void actionForRepairFollow () throws SQLException {
        EasyQuery query = QueryFactory.getWriteQuery();
        EasySQL sql = new EasySQL();
        sql.append("select * from ycbusi_ekf.c_bo_order_follow cbof where TASK_NAME ='发起调解' and  CREATE_TIME >='2025-06-24 00:41:42' and CONTENT -> '$.c_box_mediate_order.MEDIATE_NODE' is  null");
        List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(jsonObject -> {
                try {
                    String orderId = jsonObject.getString("ORDER_ID");
                    EasySQL sql1 = new EasySQL();
                    sql1.append("select * from ycbusi_ekf.c_box_mediate_order ");
                    sql.append(orderId,"where M_ID = ?");
                    JSONObject json = query.queryForRow(sql1.getSQL(), sql1.getParams(), new JSONMapperImpl());
                    if (json != null) {
                        //{
                        //  "c_box_mediate_order": {
                        //    "MEDIATE_NODE": "发起调解",
                        //    "MEDIATE_TYPE": "02",
                        //    "MEDIATE_INITIATOR": "张笑思[申诉中心]",
                        //    "MEDIATE_APPLY_TIME": "2025-06-24 08:42:52",
                        //    "PROVINCE_CODE": "110000",
                        //    "CITY_CODE": "1101",
                        //    "ENT_TYPE": "1",
                        //    "ENT_DEPT_CODE": "503502",
                        //    "ENT_DEPT_NAME": "#4a15fa214eafb900cf741b2a2041f716",
                        //    "MEDIATE_APPEAL": "部转-20250600219241",
                        //    "CLASS_CODE1": "82994320044648222597818",
                        //    "CLASS_CODE2": "82994320044108222064335",
                        //    "CLASS_CODE3": "82994320043998221955075",
                        //    "P_ID": "17489106572801125303778",
                        //    "P_ORDER_NO": "部转-20250600030902"
                        //  }}
                        String mediateNode = json.getString("MEDIATE_NODE");
                        String mediateType = json.getString("MEDIATE_TYPE");
                        String mediateInitiator = json.getString("MEDIATE_INITIATOR");
                        String mediateApplyTime = json.getString("MEDIATE_APPLY_TIME");
                        String provinceCode = json.getString("PROVINCE_CODE");
                        String cityCode = json.getString("CITY_CODE");
                        String entType = json.getString("ENT_TYPE");
                        String entDeptCode = json.getString("ENT_DEPT_CODE");
                        String entDeptName = json.getString("ENT_DEPT_NAME");
                        String mediateAppeal = json.getString("MEDIATE_APPEAL");
                        String classCode1 = json.getString("CLASS_CODE1");
                        String classCode2 = json.getString("CLASS_CODE2");
                        String classCode3 = json.getString("CLASS_CODE3");
                        String pId = json.getString("P_ID");
                        String pOrderNo = json.getString("P_ORDER_NO");
                        String mediateId = json.getString("ID");
                        JSONObject mediate =  new JSONObject();
                        mediate.put("MEDIATE_NODE", mediateNode);
                        mediate.put("MEDIATE_TYPE", mediateType);
                        mediate.put("MEDIATE_INITIATOR", mediateInitiator);
                        mediate.put("MEDIATE_APPLY_TIME", mediateApplyTime);
                        mediate.put("PROVINCE_CODE", provinceCode);
                        mediate.put("CITY_CODE", cityCode);
                        mediate.put("ENT_TYPE", entType);
                        mediate.put("ENT_DEPT_CODE", entDeptCode);
                        mediate.put("ENT_DEPT_NAME", entDeptName);
                        mediate.put("MEDIATE_APPEAL", mediateAppeal);
                        if (StringUtils.isNotBlank(mediateNode)) {
                            EasyRecord record = new EasyRecord(getTableName("c_bo_order_follow"),"ID");
                            record.set("ID", jsonObject.getString("ID"));
                            record.put("CONTENT",new JSONObject(){{put("c_box_mediate_order",mediate);}}.toJSONString());
                            query.update(record);
                        }
                    }
                } catch (SQLException e) {
                    CommonLogger.getLogger().error(e.getMessage(), e);
                    throw new RuntimeException(e);
                }

            });
        }
    }

}


