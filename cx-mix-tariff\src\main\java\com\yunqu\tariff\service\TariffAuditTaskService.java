package com.yunqu.tariff.service;

import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.nio.charset.Charset;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.slf4j.Logger;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.enums.AuditTaskStatusEnum;
import com.yunqu.tariff.enums.ProcessStatusEnum;
import com.yunqu.tariff.utils.TariffCompareUtil;
import com.yunqu.tariff.utils.TariffExcelUtil;
import com.yunqu.tariff.utils.TariffZipUtil;
import com.yunqu.tariff.utils.EnhancedZipUtil;
import com.yunqu.tariff.utils.ZipFileValidator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpUtil;

/**
 * 稽核任务业务逻辑层
 * <p>
 * 负责处理稽核任务的创建、状态更新、数据处理等核心业务逻辑。
 * 主要功能包括：
 * <ul>
 * <li>创建稽核任务并保存到数据库</li>
 * <li>更新任务状态和处理进度</li>
 * <li>执行稽核任务的业务逻辑</li>
 * <li>统计稽核结果并生成报告</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-21
 */
public class TariffAuditTaskService {

    private static final Logger logger = CommonLogger.getLogger("audit-task");

    /**
     * 创建稽核任务
     */
    public static EasyResult createTask(JSONObject taskData, File zipFile, String entId, String busiOrderId,
            String userAcc, String userName) {
        try {
            // 验证必填参数
            if (taskData == null) {
                return EasyResult.error(400, "任务数据不能为空");
            }

            String taskName = taskData.getString("taskName");
            String provinceCode = taskData.getString("provinceCode");
            String provinceName = taskData.getString("provinceName");
            String entCode = taskData.getString("entCode");
            String entName = taskData.getString("entName");
            String salesFileName = taskData.getString("salesFileName");
            String ordersFileName = taskData.getString("ordersFileName");
            String productIdColumn = taskData.getString("productIdColumn");
            String productNameColumn = taskData.getString("productNameColumn");
            String productCodeColumn = taskData.getString("productCodeColumn");
            String orderProductIdColumn = taskData.getString("ordersProductIdColumn");
            String orderCustPhoneCulumn = taskData.getString("ordersCustPhoneColumn");
            String orderAreaCodeColumn = taskData.getString("ordersAreaCodeColumn");
            String orderTimeColumn = taskData.getString("ordersTimeColumn");
            String realZipFileName = taskData.getString("fileName");

            // 验证必填字段
            /*
             * if (StringUtils.isBlank(taskName) || StringUtils.isBlank(provinceCode) ||
             * StringUtils.isBlank(entCode) || StringUtils.isBlank(salesFileName) ||
             * StringUtils.isBlank(productIdColumn) ||
             * StringUtils.isBlank(productNameColumn) ||
             * StringUtils.isBlank(productCodeColumn)) {
             * return EasyResult.error(400, "必填字段不能为空");
             * }
             */

            // 验证ZIP文件
            if (zipFile == null || !zipFile.exists()) {
                return EasyResult.error(400, "ZIP文件不存在");
            }

            // 验证文件大小
            if (zipFile.length() > Constants.getAuditMaxFileSize()) {
                return EasyResult.error(400, "文件大小超过限制（最大50MB）");
            }

            // 生成任务ID
            String taskId = IDGenerator.getDefaultNUMID();
            String currentTime = DateUtil.getCurrentDateStr();

            // 创建任务记录
            EasyRecord record = new EasyRecord(Constants.getBusiTable(Constants.AUDIT_TASK_TABLE), "ID");
            record.put("ID", taskId);
            record.put("TASK_NAME", taskName);
            record.put("REAL_ZIP_FILE_NAME", realZipFileName);
            record.put("PROVINCE_CODE", provinceCode);
            record.put("PROVINCE_NAME", provinceName);
            record.put("ENT_CODE", entCode);
            record.put("ENT_NAME", entName);
            record.put("SALES_FILE_NAME", salesFileName);
            record.put("ORDERS_FILE_NAME", ordersFileName);
            record.put("PRODUCT_ID_COLUMN", productIdColumn);
            record.put("PRODUCT_NAME_COLUMN", productNameColumn);
            record.put("PRODUCT_CODE_COLUMN", productCodeColumn);
            record.put("ORDERS_PRODUCT_ID_COLUMN", orderProductIdColumn);
            record.put("ORDERS_CUST_PHONE_COLUMN", orderCustPhoneCulumn);
            record.put("ORDERS_AREA_CODE_COLUMN", orderAreaCodeColumn);
            record.put("ORDERS_TIME_COLUMN", orderTimeColumn);
            record.put("ZIP_FILE_NAME", zipFile.getName());
            record.put("ZIP_FILE_SIZE", zipFile.length());
            record.put("STATUS", AuditTaskStatusEnum.PENDING.getCode());
            record.put("LOCAL_PROCESS_STATUS", ProcessStatusEnum.NOT_STARTED.getCode());
            record.put("SFTP_UPLOAD_STATUS", ProcessStatusEnum.NOT_STARTED.getCode());
            record.put("REMOTE_PROCESS_STATUS", ProcessStatusEnum.NOT_STARTED.getCode());
            record.put("TOTAL_COUNT", 0);
            record.put("UNPUBLIC_COUNT", 0);
            record.put("UNREPORT_COUNT", 0);
            record.put("CREATE_TIME", currentTime);
            record.put("CREATE_ACC", userAcc);
            record.put("CREATE_NAME", userName);
            record.put("ENT_ID", entId);
            record.put("BUSI_ORDER_ID", busiOrderId);

            // 保存到数据库
            QueryFactory.getTariffQuery().save(record);

            // 移动ZIP文件到临时目录
            String tempDir = Constants.getAuditTempDir();
            File tempDirFile = new File(tempDir);
            if (!tempDirFile.exists()) {
                tempDirFile.mkdirs();
            }

            String zipFileName = taskId + "_" + zipFile.getName();
            File targetFile = new File(tempDir, zipFileName);

            // 移动文件
            if (zipFile.renameTo(targetFile)) {
                // 更新文件路径
                EasyRecord updateRecord = new EasyRecord(Constants.getBusiTable(Constants.AUDIT_TASK_TABLE), "ID");
                updateRecord.setPrimaryValues(taskId);
                updateRecord.set("ZIP_FILE_PATH", targetFile.getAbsolutePath());
                updateRecord.set("UPDATE_TIME", DateUtil.getCurrentDateStr());
                QueryFactory.getTariffQuery().update(updateRecord);

                logger.info("稽核任务创建成功，任务ID: " + taskId + ", 任务名称: " + taskName);

                JSONObject result = new JSONObject();
                result.put("taskId", taskId);
                result.put("taskName", taskName);
                return EasyResult.ok(result);
            } else {
                return EasyResult.error(500, "文件保存失败");
            }

        } catch (SQLException e) {
            logger.error("创建稽核任务失败", e);
            return EasyResult.error(500, "数据库操作失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("创建稽核任务失败", e);
            return EasyResult.error(500, "系统异常: " + e.getMessage());
        }
    }

    /**
     * 更新任务状态
     */
    public static EasyResult updateTaskStatus(String taskId, AuditTaskStatusEnum status, String errorMessage) {
        try {
            if (StringUtils.isBlank(taskId) || status == null) {
                return EasyResult.error(400, "参数不能为空");
            }

            EasyRecord record = new EasyRecord(Constants.getBusiTable(Constants.AUDIT_TASK_TABLE), "ID");
            record.setPrimaryValues(taskId);
            record.set("STATUS", status.getCode());
            record.set("UPDATE_TIME", DateUtil.getCurrentDateStr());

            if (StringUtils.isNotBlank(errorMessage)) {
                record.set("ERROR_MESSAGE", errorMessage);
            }

            boolean update = QueryFactory.getTariffQuery().update(record);

            if (update) {
                logger.info("任务状态更新成功，任务ID: {}, 状态: {}", taskId, status.getDesc());
                return EasyResult.ok();
            } else {
                logger.error("任务状态更新失败，任务ID: {}", taskId);
                return EasyResult.error(404, "任务不存在或更新失败");
            }

        } catch (SQLException e) {
            logger.error("更新任务状态失败，任务ID: {}", taskId, e);
            return EasyResult.error(500, "数据库操作失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("更新任务状态失败，任务ID: {}", taskId, e);
            return EasyResult.error(500, "系统异常: " + e.getMessage());
        }
    }

    /**
     * 更新处理状态
     */
    public static EasyResult updateProcessStatus(String taskId, String statusType, ProcessStatusEnum status) {
        try {
            if (StringUtils.isBlank(taskId) || StringUtils.isBlank(statusType) || status == null) {
                return EasyResult.error(400, "参数不能为空");
            }

            String columnName;
            switch (statusType.toLowerCase()) {
                case "local":
                    columnName = "LOCAL_PROCESS_STATUS";
                    break;
                case "sftp":
                    columnName = "SFTP_UPLOAD_STATUS";
                    break;
                case "remote":
                    columnName = "REMOTE_PROCESS_STATUS";
                    break;
                default:
                    return EasyResult.error(400, "无效的状态类型: " + statusType);
            }

            String sql = "UPDATE " + Constants.getBusiTable(Constants.AUDIT_TASK_TABLE) +
                    " SET " + columnName + " = ?, UPDATE_TIME = ? WHERE ID = ?";

            int result = QueryFactory.getTariffQuery().executeUpdate(sql,
                    new Object[] { status.getCode(), DateUtil.getCurrentDateStr(), taskId });

            if (result > 0) {
                logger.info("处理状态更新成功，任务ID: {}, 类型: {}, 状态: {}", taskId, statusType, status.getDesc());
                return EasyResult.ok();
            } else {
                logger.error("处理状态更新失败，任务ID: {}", taskId);
                return EasyResult.error(404, "任务不存在或更新失败");
            }

        } catch (SQLException e) {
            logger.error("更新处理状态失败，任务ID: {}", taskId, e);
            return EasyResult.error(500, "数据库操作失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("更新处理状态失败，任务ID: {}", taskId, e);
            return EasyResult.error(500, "系统异常: " + e.getMessage());
        }
    }

    /**
     * 更新统计信息
     */
    public static EasyResult updateStatistics(String taskId, int totalCount, int unpublicCount,
            int unreportCount, String unpublicFilePath, String unreportFilePath, String unpublicFileName,
            String unreportFileName) {
        try {
            if (StringUtils.isBlank(taskId)) {
                return EasyResult.error(400, "任务ID不能为空");
            }

            EasyRecord record = new EasyRecord(Constants.getBusiTable(Constants.AUDIT_TASK_TABLE), "ID");
            record.setPrimaryValues(taskId);
            record.set("TOTAL_COUNT", totalCount);
            record.set("UNPUBLIC_COUNT", unpublicCount);
            record.set("UNREPORT_COUNT", unreportCount);
            record.set("UPDATE_TIME", DateUtil.getCurrentDateStr());

            if (StringUtils.isNotBlank(unpublicFilePath)) {
                record.set("UNPUBLIC_FILE_PATH", unpublicFilePath);
            }
            if (StringUtils.isNotBlank(unpublicFileName)) {
                record.set("UNPUBLIC_FILE_NAME", unpublicFileName);
            }
            if (StringUtils.isNotBlank(unreportFilePath)) {
                record.set("UNREPORT_FILE_PATH", unreportFilePath);
            }
            if (StringUtils.isNotBlank(unreportFileName)) {
                record.set("UNREPORT_FILE_NAME", unreportFileName);
            }

            boolean update = QueryFactory.getTariffQuery().update(record);
            if (update) {
                logger.info("统计信息更新成功，任务ID: " + taskId + ", 总数: " + totalCount +
                        ", 未公示: " + unpublicCount + ", 未报送: " + unreportCount);
                return EasyResult.ok();
            } else {
                return EasyResult.error(404, "任务不存在或更新失败");
            }

        } catch (SQLException e) {
            logger.error("更新统计信息失败，任务ID: " + taskId, e);
            return EasyResult.error(500, "数据库操作失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("更新统计信息失败，任务ID: " + taskId, e);
            return EasyResult.error(500, "系统异常: " + e.getMessage());
        }
    }

    /**
     * 记录处理时间
     */
    public static EasyResult updateProcessTime(String taskId, String startTime, String endTime) {
        try {
            if (StringUtils.isBlank(taskId)) {
                return EasyResult.error(400, "任务ID不能为空");
            }

            EasyRecord record = new EasyRecord(Constants.getBusiTable(Constants.AUDIT_TASK_TABLE), "ID");
            record.setPrimaryValues(taskId);
            record.set("UPDATE_TIME", DateUtil.getCurrentDateStr());

            if (StringUtils.isNotBlank(startTime)) {
                record.set("PROCESS_START_TIME", startTime);
            }
            if (StringUtils.isNotBlank(endTime)) {
                record.set("PROCESS_END_TIME", endTime);
            }

            boolean update = QueryFactory.getTariffQuery().update(record);

            if (update) {
                logger.info("处理时间更新成功，任务ID: {}", taskId);
                return EasyResult.ok();
            } else {
                logger.error("处理时间更新失败，任务ID: {}", taskId);
                return EasyResult.error(404, "任务不存在或更新失败");
            }

        } catch (SQLException e) {
            logger.error("更新处理时间失败，任务ID: {}", taskId, e);
            return EasyResult.error(500, "数据库操作失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("更新处理时间失败，任务ID: {}", taskId, e);
            return EasyResult.error(500, "系统异常: " + e.getMessage());
        }
    }


    private static JSONObject buildColumnsConfigObject(JSONObject task) {
        JSONObject config = new JSONObject();
        JSONObject productConfig = new JSONObject();
        productConfig.put("produnctFileName", task.getString("SALES_FILE_NAME"));
        productConfig.put("productIdColumnId", task.getString("PRODUCT_ID_COLUMN"));
        productConfig.put("productNameColumn", task.getString("PRODUCT_NAME_COLUMN"));
        productConfig.put("productCodeColumn", task.getString("PRODUCT_CODE_COLUMN"));
        JSONObject orderConfig = new JSONObject();
        orderConfig.put("ordersFileName", task.getString("ORDERS_FILE_NAME"));
        orderConfig.put("orderProductIdColumn", task.getString("ORDERS_PRODUCT_ID_COLUMN"));
        orderConfig.put("orderCustPhoneColumn", task.getString("ORDERS_CUST_PHONE_COLUMN"));
        orderConfig.put("orderAreaCodeColumn", task.getString("ORDERS_AREA_CODE_COLUMN"));
        orderConfig.put("orderTimeColumn", task.getString("ORDERS_TIME_COLUMN"));
        config.put("productConfig", productConfig);
        config.put("orderConfig", orderConfig);
        return config;
    }

    /**
     * 处理单个稽核任务
     */
    public static EasyResult processTask(String taskId) {
        logger.info("开始处理稽核任务: {}", taskId);

        try {
            // 1. 查询任务信息
            JSONObject task = getTaskById(taskId);
            if (task == null) {
                return EasyResult.error(404, "任务不存在");
            }

            // 2. 检查任务状态
            int status = task.getIntValue("STATUS");
            if (status != AuditTaskStatusEnum.PENDING.getCode()) {
                logger.error("任务状态不正确，当前状态: {}", status);
                return EasyResult.error(400, "任务状态不正确，当前状态: " + status);
            }

            // 3. 更新任务状态为执行中
            updateTaskStatus(taskId, AuditTaskStatusEnum.PROCESSING, null);
            updateProcessTime(taskId, DateUtil.getCurrentDateStr(), null);

            String salesFileName = task.getString("SALES_FILE_NAME");
            if (StringUtils.isNotBlank(salesFileName)) {
                updateProcessStatus(taskId, "local", ProcessStatusEnum.PROCESSING);
            }

            // 4. 验证并解压ZIP文件
            String zipFilePath = task.getString("ZIP_FILE_PATH");
            String extractPath = Constants.getAuditUnzipDir() + File.separator + taskId;

            // 首先验证ZIP文件
            ZipFileValidator.ValidationResult validation = ZipFileValidator.validateZipFile(zipFilePath);
            if (!validation.isValid()) {
                updateTaskStatus(taskId, AuditTaskStatusEnum.FAILED, "ZIP文件验证失败: " + validation.getErrorMessage());
                logger.error("ZIP文件验证失败，任务: {}, 错误: {}", taskId, validation.getErrorMessage());
                return EasyResult.error(500, "ZIP文件验证失败: " + validation.getErrorMessage());
            }

            // 使用增强的ZIP工具解压文件
            List<String> extractedFiles = extractZipFileWithEnhancedUtil(zipFilePath, extractPath, validation.getSuggestedCharset());
            if (extractedFiles.isEmpty()) {
                updateTaskStatus(taskId, AuditTaskStatusEnum.FAILED, "ZIP文件解压失败");
                logger.error("ZIP文件解压失败，任务: {}", taskId);
                return EasyResult.error(500, "ZIP文件解压失败");
            }

            // 更新解压路径
            updateFilePaths(taskId, zipFilePath, extractPath, null);

            // 5. 查找并解析Excel文件
            if (StringUtils.isNotBlank(salesFileName)) {
                String excelFilePath = TariffZipUtil.findExcelFilePath(extractPath, salesFileName);

                if (StringUtils.isBlank(excelFilePath)) {
                    updateTaskStatus(taskId, AuditTaskStatusEnum.FAILED, "未找到指定的Excel文件: " + salesFileName);
                    logger.error("未找到指定的Excel文件: {}，任务: {}", salesFileName, taskId);
                    return EasyResult.error(500, "未找到指定的Excel文件");
                }

                // 6. 解析Excel数据
                String productIdColumn = task.getString("PRODUCT_ID_COLUMN");
                String productNameColumn = task.getString("PRODUCT_NAME_COLUMN");
                String productCodeColumn = task.getString("PRODUCT_CODE_COLUMN");

                List<JSONObject> productData = TariffExcelUtil.parseExcelFile(excelFilePath, productIdColumn,
                        productNameColumn, productCodeColumn);

                if (productData.isEmpty()) {
                    updateTaskStatus(taskId, AuditTaskStatusEnum.FAILED, "Excel文件解析失败或无有效数据");
                    logger.error("Excel文件解析失败或无有效数据，任务: {}", taskId);
                    return EasyResult.error(500, "Excel文件解析失败或无有效数据");
                }

                // 7. 数据对比
                String provinceCode = task.getString("PROVINCE_CODE");
                String entCode = task.getString("ENT_CODE");

                JSONObject compareResult = TariffCompareUtil.batchCompare(productData, provinceCode, entCode);

                // 8. 生成结果Excel文件
                String exportDir = Constants.getAuditExportDir() + File.separator + taskId;
                File exportDirFile = new File(exportDir);
                if (!exportDirFile.exists()) {
                    exportDirFile.mkdirs();
                }

                String taskName = task.getString("TASK_NAME");
                String timestamp = DateUtil.getCurrentDateStr("yyyyMMdd_HHmmss");

                String unpublicFilePath = "";
                String unpublicFileName = "";
                String unreportFilePath = "";
                String unreportFileName = "";

                List<JSONObject> unpublicList = (List<JSONObject>) compareResult.get("unpublicList");
                if (CollUtil.isNotEmpty(unpublicList)) {
                    unpublicFileName = "未公示资费_" + taskName + "_" + timestamp + ".xlsx";
                    unpublicFilePath = exportDir + File.separator + unpublicFileName;
                    TariffExcelUtil.exportToExcel(unpublicList, unpublicFilePath, "未公示资费清单");
                }

                List<JSONObject> unreportList = (List<JSONObject>) compareResult.get("unreportList");
                if (CollUtil.isNotEmpty(unreportList)) {
                    unreportFileName = "未报送资费_" + taskName + "_" + timestamp + ".xlsx";
                    unreportFilePath = exportDir + File.separator + unreportFileName;
                    TariffExcelUtil.exportToExcel(unreportList, unreportFilePath, "未报送资费清单");
                }

                // 9. 更新统计信息
                int totalCount = compareResult.getIntValue("totalCount");
                int unpublicCount = compareResult.getIntValue("unpublicCount");
                int unreportCount = compareResult.getIntValue("unreportCount");

                updateStatistics(taskId, totalCount, unpublicCount, unreportCount, unpublicFilePath, unreportFilePath,
                        unpublicFileName, unreportFileName);

                // 10. 更新本地处理状态为完成
                updateProcessStatus(taskId, "local", ProcessStatusEnum.COMPLETED);
                updateTaskStatus(taskId, AuditTaskStatusEnum.LOCAL_COMPLETED, null);
                updateProcessTime(taskId, null, DateUtil.getCurrentDateStr());

                logger.info("稽核任务处理完成: " + taskId + ", 总数: " + totalCount +
                        ", 未公示: " + unpublicCount + ", 未报送: " + unreportCount);
            }

            // 11. 推送结果到SFTP服务器
            String ordersFileName = task.getString("ORDERS_FILE_NAME");
            if (StringUtils.isNotBlank(ordersFileName)) {
                JSONObject columnsConfig = buildColumnsConfigObject(task);
                String sftpRemoteFolder = sendOrdersFileToSftp(taskId, zipFilePath, columnsConfig);
                if (StringUtils.isNotBlank(sftpRemoteFolder)) {
                    updateFtpRemoteInfo(taskId, sftpRemoteFolder);
                }
            }

            return EasyResult.ok("任务处理完成");

        } catch (Exception e) {
            logger.error("处理稽核任务失败: {}", taskId, e);
            updateTaskStatus(taskId, AuditTaskStatusEnum.FAILED, "处理异常: " + e.getMessage());
            return EasyResult.error(500, "处理异常: " + e.getMessage());
        }
    }

    /**
     * 更新文件路径信息
     *
     * @param taskId         任务ID
     * @param sftpRemotePath SFTP远程路径
     * @return
     */
    private static EasyResult updateFtpRemoteInfo(String taskId, String sftpRemotePath) {
        try {
            EasyRecord recod = new EasyRecord(Constants.AUDIT_TASK_TABLE, "ID").setPrimaryValues(taskId);
            recod.set("STATUS", 2);
            recod.set("SFTP_REMOTE_PATH", sftpRemotePath);
            recod.set("SFTP_UPLOAD_STATUS", 2);
            recod.set("REMOTE_PROCESS_STATUS", 1);
            boolean update = QueryFactory.getTariffQuery().update(recod);
            return update ? EasyResult.ok() : EasyResult.fail("更新文件路径信息失败");
        } catch (Exception e) {
            logger.error("更新文件路径信息失败: {}", taskId, e);
            return EasyResult.error(500, "更新文件路径信息失败: " + e.getMessage());
        }
    }

    /**
     * 推送文件到SFTP服务器
     */
    private static String sendOrdersFileToSftp(String taskId, String filePath, JSONObject columnsConfig) {

        String url = Constants.XTY_PUBLIC_HOST + "/cx-mix-public/audit/task/file";

        Map<String, Object> formData = new HashMap<>();
        formData.put("file", new File(filePath));
        formData.put("taskId", taskId);
        formData.put("columnsConfig", columnsConfig);

        logger.info("开始推送文件到SFTP服务器: url={}, taskId={}, filePath={}", url, taskId, filePath);
        String body = HttpUtil.createPost(url)
                .form(formData)
                .execute().body();
        logger.info("推送文件到SFTP服务器完成: url={}, taskId={}, result={}", url, taskId, body);

        JSONObject result = JSONObject.parseObject(body);
        int state = result.getIntValue("state");
        if (state == 1) {
            return result.getString("data");
        } else {
            return null;
        }
    }

    /**
     * 根据ID查询任务
     */
    private static JSONObject getTaskById(String taskId) throws SQLException {
        String sql = "SELECT * FROM " + Constants.getBusiTable(Constants.AUDIT_TASK_TABLE) + " WHERE ID = ?";
        return QueryFactory.getTariffQuery().queryForRow(sql, new Object[] { taskId }, new JSONMapperImpl());
    }

    /**
     * 更新文件路径
     */
    private static EasyResult updateFilePaths(String taskId, String zipFilePath, String extractPath,
            String sftpRemotePath) {
        try {
            EasyRecord record = new EasyRecord(Constants.getBusiTable(Constants.AUDIT_TASK_TABLE), "ID");
            record.setPrimaryValues(taskId);
            record.set("UPDATE_TIME", DateUtil.getCurrentDateStr());

            if (StringUtils.isNotBlank(zipFilePath)) {
                record.set("ZIP_FILE_PATH", zipFilePath);
            }
            if (StringUtils.isNotBlank(extractPath)) {
                record.set("EXTRACT_PATH", extractPath);
            }
            if (StringUtils.isNotBlank(sftpRemotePath)) {
                record.set("SFTP_REMOTE_PATH", sftpRemotePath);
            }

            boolean update = QueryFactory.getTariffQuery().update(record);
            return update ? EasyResult.ok() : EasyResult.error(500, "更新失败");

        } catch (SQLException e) {
            logger.error("更新文件路径失败，任务ID: " + taskId, e);
            return EasyResult.error(500, "数据库操作失败: " + e.getMessage());
        }
    }

    /**
     * 使用增强的ZIP工具解压文件
     */
    private static List<String> extractZipFileWithEnhancedUtil(String zipFilePath, String extractPath, Charset charset) {
        List<String> extractedFiles = new ArrayList<>();
        
        try {
            File extractDir = new File(extractPath);
            if (!extractDir.exists()) {
                extractDir.mkdirs();
            }

            try (java.util.zip.ZipInputStream zis = new java.util.zip.ZipInputStream(
                    new java.io.FileInputStream(zipFilePath), charset)) {
                
                java.util.zip.ZipEntry entry;
                byte[] buffer = new byte[8192];

                while ((entry = zis.getNextEntry()) != null) {
                    String fileName = entry.getName();

                    // 防止目录遍历攻击
                    if (fileName.contains("..") || fileName.startsWith("/")) {
                        logger.warn("跳过可疑文件: {}", fileName);
                        continue;
                    }

                    File newFile = new File(extractDir, fileName);

                    // 如果是目录，创建目录
                    if (entry.isDirectory()) {
                        newFile.mkdirs();
                        continue;
                    }

                    // 创建父目录
                    File parent = newFile.getParentFile();
                    if (parent != null && !parent.exists()) {
                        parent.mkdirs();
                    }

                    // 解压文件
                    try (java.io.FileOutputStream fos = new java.io.FileOutputStream(newFile);
                         java.io.BufferedOutputStream bos = new java.io.BufferedOutputStream(fos)) {
                        
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            bos.write(buffer, 0, len);
                        }
                        bos.flush();
                    }

                    extractedFiles.add(newFile.getAbsolutePath());
                    logger.debug("解压文件: {}", fileName);
                }

                logger.info("ZIP文件解压完成，共解压 {} 个文件到: {}", extractedFiles.size(), extractPath);

            }
        } catch (Exception e) {
            logger.error("解压ZIP文件失败: {}", zipFilePath, e);
            extractedFiles.clear();
        }

        return extractedFiles;
    }
}
