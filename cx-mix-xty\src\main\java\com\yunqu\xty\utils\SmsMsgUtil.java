package com.yunqu.xty.utils;


import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.ServiceUtil;
import com.yunqu.xty.base.CommonLogger;
import com.yunqu.xty.base.Constants;
import com.yunqu.xty.base.QueryFactory;

public class SmsMsgUtil {

    public static Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("sms").getName());

    public static JSONObject sendMsg(String orderId, String schema, String entId, String busiOrderId) throws Exception {
        JSONObject defaultChannel = getDefaultChannel(schema);
        if (Objects.isNull(defaultChannel)) {
            throw new Exception("当前不存在默认发送渠道，短信发送失败哦");
        }
		JSONObject appealOrder = OrderUtils.getAppealOrder(orderId, schema);
		if (appealOrder == null || StringUtils.isBlank(appealOrder.getString("PHONE")) ) {
			throw new Exception("当前不存在申诉工单或者联系号码，短信发送失败哦");
		}
        JSONObject exJson = new JSONObject();
        exJson.put("NOTICE_CHANNELS", StringUtils.isBlank(defaultChannel.getString("ID")) ? "6919904469289848" : defaultChannel.getString("ID"));
        exJson.put("CLASS_TYPE", "01");
        exJson.put("EX1", "appeal");
        // 参数列表
        exJson.put("MODULE_NAME", "自定义发送");
        exJson.put("SEND_TYPE", "1");
        // 根据通知号码发送
        exJson.put("QUERY_NOTICE", "02");
        // 通知号码
        exJson.put("NOTICE_NUMBERS", PhoneCryptor.getInstance().decrypt(appealOrder.getString("PHONE")));

        JSONObject smsTemplete = getSmsTemplete(schema, entId);
        if (Objects.isNull(smsTemplete)) {
            throw new Exception("当前不存在默认发送模板，短信发送失败哦");
        }
        JSONObject param = new JSONObject();
        param.put("moduleCode", Constants.APP_NAME);
        param.put("title", smsTemplete.getString("TEMPLATE_NAME"));
        // 增加固定签名
        param.put("content", "【工信部申诉中心】" + smsTemplete.getString("TEMPLATE_CONTENT"));
        param.put("exJson", exJson);
        param.put("entId", entId);
        param.put("busiOrderId", busiOrderId);
        param.put("schema", schema);
        param.put("createUser", "system");
        param.put("createUserName", "系统");
        param.put("createUserDept", "系统");
        param.put("command", ServiceCommand.TRIGGER_NOTICE_SEND);
        param.put("busiId", orderId);
        logger.info("发送短信参数：" + JSONObject.toJSONString(param));
        JSONObject result = ServiceUtil.invoke2(ServiceID.NOTICE_INTERFACE, param);
        logger.info("发送短信结果：" + JSONObject.toJSONString(result));
        if (result == null || !GWConstants.RET_CODE_SUCCESS.equals(result.getString("respCode"))) {
            throw new Exception("短信发送失败了");
        }
        return smsTemplete;
    }

     /**
     * 获取短信默认渠道
     *
     * @param schema
     * @return
     * @throws SQLException
     */
    private static JSONObject getDefaultChannel(String schema) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append(" select * from " + schema + ".c_sms_channel ");
        sql.append(" where 1=1 ");
        sql.append("1", " and STATUS = ? ");
        sql.append("1", "and DEFAULT_CHANEL =? ");
        return QueryFactory.getWriteQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
    }

    /**
     * 获取短信模板
     *
     * @param schema
     * @return
     * @throws SQLException
     */
    private static JSONObject getSmsTemplete(String schema, String entId) throws SQLException {
        JSONObject cache = GlobalConfigUtil.getInstance().getCache(entId, schema, "2");
        if (Objects.nonNull(cache)) {
            return cache.getJSONObject("IMPORT_SMS_TEMPLETE");
        }
        return null;
    }
    
}
