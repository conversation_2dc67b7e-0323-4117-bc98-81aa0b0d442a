package com.yunqu.tariff.inf.tariff;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.mq.MQBrokerUtil;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.container.XtyTariffContainer;
import com.yunqu.tariff.exception.TariffAuditException;
import com.yunqu.tariff.service.TariffAuditProcessStorageService;
import com.yunqu.tariff.service.TariffPublicService;
import com.yunqu.tariff.thread.ThreadPoolManager;
import com.yunqu.tariff.utils.BusiUtil;
import com.yunqu.xty.commonex.util.RedissonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 * 获取资费稽核结果
 * </p>
 *
 * @ClassName TariffGetAuditResultServiceExecutor
 * <AUTHOR> Copy This Tag)
 * @Description 获取资费稽核结果
 * @Since create in 7/1/24 10:00 AM
 * @Version v1.0
 * @Copyright Copyright (c) 2024
 * @Company 广州云趣信息科技有限公司
 */
public class TariffGetAuditResultServiceExecutor implements TariffServiceExecutor {

    private static final Logger jobbuslogger = LoggerFactory.getLogger(CommonLogger.getJobBusLogger().getName());

    private static final Logger joblogger = LoggerFactory.getLogger(CommonLogger.getJobLogger().getName());

    private static final Logger joberrlogger = LoggerFactory.getLogger(CommonLogger.getJobErrLogger().getName());

    private final Cache<String, String> CACHE = Caffeine.newBuilder()
            .build();

    private static final EasySQL insertTariffAuditSql = new EasySQL()
            .append("insert into " + Constants.getBusiSchema() + ".XTY_TARIFF_AUDIT")
            .append("(ID,TARIFF_RECORD_ID,ENT,ENT_NAME,EXIST_REPORT,REPORT_NO,TARIFF_NAME,AUDIT_DATE,ORDER_NUM,CREATE_TIME,SOURCE,TARIFF_LIKE_NAME,PROVINCE_CODE,PLAN_RECORD_ID,TARIFF_TYPE,DATE_ID,MONTH_ID,TARIFF_PROVINCE_CODE,ERR_REPORT_NO)")
            .append(" values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

    private static final EasySQL insertTariffOrderAreaSql = new EasySQL()
            .append("insert into " + Constants.getBusiSchema() + ".XTY_TARIFF_ORDER_AREA")
            .append("(ID,TARIFF_AUDIT_ID,SOURCE,AREA_CODE,AREA_NAME,ORDER_NUM,CREATE_TIME,PROVINCE_CODE,PROVINCE_NAME,DATE_ID,TARIFF_TYPE,ENT,TARIFF_PROVINCE_CODE)")
            .append(" values(?,?,?,?,?,?,?,?,?,?,?,?,?)");

    private static final EasySQL insertTariffOrderUsersql = new EasySQL()
            .append("insert into " + Constants.getBusiSchema() + ".XTY_TARIFF_ORDER_USER")
            .append("(ID,TARIFF_RECORD_ID,REPORT_NO,ORDER_TIME,AREA_CODE,AREA_NAME,PHONENUM,ORDER_BAKUP,CREATE_TIME,BAKUP,SOURCE,TARIFF_AUDIT_ID," +
                    "TARIFF_ORDER_AREA_ID,IS_VALID,PROVINCE_CODE,PROVINCE_NAME, EXIST_REPORT,TARIFF_NAME,ENT,ENT_NAME,TARIFF_TYPE,TARIFF_PROVINCE_CODE,AUDIT_DATE,MONTH_ID,DATE_ID)")
            .append(" values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

    /**
     * 稽核服务器获取备案信息，完成稽核后，将结果导出为UTF-8编码的JSON文件，压缩为zip后，以"备案主体-稽核日期-result.zip"为文件名，传回SFTP前一日目录下，由综合管理平台解析入库。
     * 对于已备案订单，稽核结果包含：备案号，订单总量。对于未备案，稽核结果包含：名称、订单总量、销售地区及销售数量列表。
     *
     * @param serviceCode 服务编码
     * @param param       参数
     *                    {dateId:稽核日期,provinceCode:省份编码,entCode:企业编码,tariffProvinceCode:资费省份编码}
     * @return 结果
     */
    @Override
    public JSONObject execute(String serviceCode, JSONObject param) {
        String serialId = IdUtil.simpleUUID();
        jobbuslogger.info("<{}>重新稽核开始执行服务[{}],参数:{}", serialId, serviceCode, param.toString());
        try {
            // 稽核日期
            String auditDate = param.getString("dateId");
            if (StringUtils.isNotBlank(auditDate)) {
                auditDate = auditDate.substring(0, 4) + "-" + auditDate.substring(4, 6) + "-"
                        + auditDate.substring(6, 8);
            } else {
                auditDate = DateUtil.addDay("yyyy-MM-dd", DateUtil.getCurrentDateStr("yyyy-MM-dd"), -1);
            }
            jobbuslogger.info("开始[{}]数据稽核,稽核省份:[{}],稽核省份编码:[{}],稽核运营商:[{}]", auditDate,
                    param.getString("tariffProvinceCode"), param.getString("provinceCode"), param.getString("entCode"));
            if (ObjectUtils.isEmpty(RedissonUtil.hget("TARIFF_PROVINCE_AREA" , "0280"))) {
                // 重新加载
                jobbuslogger.info("{} 进行重新加载资费省份区域...", CommonUtil.getClassNameAndMethod(this));
                reloadProviceAndArea();
            }
            param.put("type", "audit");

            // 发送mq到外网机器，让外网机器读取文件获取到文件的内容整到json内容传回来
            JSONObject resultObj = MQBrokerUtil.sendQueueMsgAndGetResult(Constants.getTraiffAuditLoadBroker(), param,
                    3600);
            if(resultObj==null) {
                return EasyResult.fail("未发现需稽核的文件");
            }
            EasyResult easyResult = analyzeAuditResult(resultObj, auditDate);
            if (easyResult != null) {
                return easyResult;
            }
        } catch (Exception e) {
            jobbuslogger.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        } finally {
            jobbuslogger.info("<{}>重新稽核结束执行服务[{}]", serialId, serviceCode);
        }
        return null;
    }

    /**
     * 解析返回的稽核结果
     *
     * @param resultObj 稽核结果
     * @param auditDate 稽核日期
     * @return 结果
     * @throws Exception 异常
     */
    private EasyResult analyzeAuditResult(JSONObject resultObj, String auditDate) throws Exception {
        if (resultObj == null) {
            jobbuslogger.error("稽核结果为空！");
            return EasyResult.fail("稽核结果为空！");
        }
        JSONArray keys = resultObj.getJSONArray("keys");
        if (keys == null) {
            jobbuslogger.error("keys为空");
            return EasyResult.fail("keys为空");
        }
        int totalAuditResultFileSize = keys.size();
        for (int i = 0; i < keys.size(); i++) {
            String redisKey = keys.getString(i);
            joblogger.info(">>>开始解析稽核结果文件:{}, 共计集合文件【{}】，当前为第【{}】个稽核文件", redisKey, totalAuditResultFileSize, i + 1);
            String dataString = CacheUtil.get(redisKey);
            if (StringUtils.isBlank(dataString)) {
                joblogger.error("[{}]获取不到缓存数据", redisKey);
                return EasyResult.error(500, "[" + redisKey + "]获取不到缓存数据");
            }
            analyzeSigleData(auditDate, dataString, redisKey);
            CacheUtil.delete(redisKey);
        }
        return EasyResult.ok(keys);
    }

    /**
     * 统计资费方案记录
     *
     * @param reStatPlanRecordIds 需要统计的资费方案记录id集合
     */
    private void reStatPlanRecord(Set<JSONObject> reStatPlanRecordIds, String auditDate) {
        if (CollectionUtils.isEmpty(reStatPlanRecordIds)) return;

        // 创建CompletableFuture任务列表
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (JSONObject reStatPlanRecord : reStatPlanRecordIds) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    statPlanRecord(reStatPlanRecord.getString("ID"),
                                 reStatPlanRecord.getString("TARIFF_TYPE"),
                                 null, reStatPlanRecord, null, null, null, null, auditDate);
                } catch (Exception e) {
                    joblogger.error(e.getMessage(), e);
                }
            }, ThreadPoolManager.getExecutorService());

            futures.add(future);
        }
        try {
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            joblogger.error("处理未备案资费方案数据重置失败", e);
        }
    }

    /**
     * 解析单个文件内容
     *
     * @param auditDate  稽核日期
     * @param dataString 文件内容
     * @param redisKey   缓存key
     * @throws Exception 异常
     */
    private void analyzeSigleData(String auditDate, String dataString, String redisKey) throws Exception {
        List<JSONObject> datas = JSON.parseObject(dataString, new TypeReference<List<JSONObject>>() {
        });
        if (datas != null) {
            auditHandle(datas, redisKey, auditDate);
        }
    }

    /**
     * 处理稽核结果
     *
     * @param datas     文件内容
     * @param redisKey  缓存key
     * @param auditDate 稽核日期
     * @throws Exception 异常
     */
    private void auditHandle(List<JSONObject> datas, String redisKey, String auditDate) throws Exception {
        // 创建时间
        String createTime = DateUtil.getCurrentDateStr();
        List<Object[]> tariffOrderUserValues = new ArrayList<>();
        List<Object[]> tariffOrderAreaValues = new ArrayList<>();
        List<Object[]> tariffAuditValues = new ArrayList<>();
        String[] split = StringUtils.split(auditDate, "-");
        String monthId = split[0]+split[1];
        String dateId = split[0]+split[1]+split[2];
        for (JSONObject dataJson : datas) {
            if (dataJson==null||dataJson.isEmpty()) {
                continue;
            }
            String fileName = dataJson.getString("fileName");
            try {
                List<String> unreportedTariffPlanIds = new ArrayList<>();
                List<String> offlineTariffPlanIds = new ArrayList<>();
                Map<String, String> linkMap = new HashMap<>();
                joblogger.info("[TariffGetAuditResultServiceExecutor] -> auditHandle 开始解析json文件:{}", fileName);
                EasyRecord auditRecord = new EasyRecord(Constants.getBusiSchema() + ".xty_tariff_audit_result");

                String reporter = fileName.split("-")[0];
                String tariffProvinceCode = reporter.substring(0, reporter.length() - 1);
                String entCode = reporter.substring(reporter.length() - 1);
                JSONObject province = XtyTariffContainer.getProvice(tariffProvinceCode);
                String provinceCode = "";
                if (province != null) {
                    provinceCode = province.getString("PROVINCE_CODE");
                }

                Set<JSONObject> reStatPlanRecordIds = new HashSet<>();

                // 删除旧数据
                delOldAudit(auditDate, provinceCode, tariffProvinceCode, entCode, reStatPlanRecordIds, dateId, fileName, monthId);

                // 未上报资费方案稽核
                unreportTariffAudit(redisKey, auditDate, dataJson, fileName, tariffAuditValues,
                        createTime, tariffOrderAreaValues,
                        tariffOrderUserValues, unreportedTariffPlanIds, linkMap, auditRecord, tariffProvinceCode, monthId, dateId);

                tariffOrderUserValues.clear();
                tariffOrderAreaValues.clear();
                tariffAuditValues.clear();

                // 续订资费方案稽核
                offlinedTariffAudit(redisKey, auditDate, dataJson, fileName, tariffAuditValues,
                        createTime, tariffOrderAreaValues,
                        tariffOrderUserValues, offlineTariffPlanIds, linkMap, auditRecord, tariffProvinceCode, monthId, dateId);

                // 处理未备案资费方案数据重置处理
                handleunreportedDataInit(unreportedTariffPlanIds, linkMap, "1", tariffProvinceCode, auditDate);
                // 处理续订资费方案重置处理
                handleunreportedDataInit(offlineTariffPlanIds, linkMap, "2", tariffProvinceCode, auditDate);

                // 移除已在未备案或下线资费方案中的记录
                reStatPlanRecordIds.removeIf(record ->
                        unreportedTariffPlanIds.contains(record.getString("ID")) ||
                                offlineTariffPlanIds.contains(record.getString("ID")));

                reStatPlanRecord(reStatPlanRecordIds, auditDate);
                auditRecord.put("PROVINCE_CODE", provinceCode);
                auditRecord.put("TARIFF_PROVINCE_CODE", tariffProvinceCode);
                auditRecord.put("ENT", entCode);
                auditRecord.put("CREATE_TIME", EasyDate.getCurrentDateString());
                joblogger.info("[TariffGetAuditResultServiceExecutor] ->====开始入库稽核结果数据=={}", auditRecord.toJSONString());
                QueryFactory.getWriteQuery().save(auditRecord);
                // 处理稽核结果到ES
                //TariffExtService.getInstance().analyzeTariffAudit(dataJson, fileName, auditDate);
                TariffAuditProcessStorageService.getInstance().execute(dataJson, fileName, auditDate);
                //开始处理公示率统计日报计算
                TariffPublicService.getInstance().syncDayPublicInfo(dataJson, provinceCode,entCode, auditDate,fileName,tariffProvinceCode);
            } catch (TariffAuditException e) {
                joblogger.error(e.getMessage(), e);
                joberrlogger.error("文件：【{}】执行异常；异常原因：{}; {}", e.getAuditFileName(), e.getMessage(), e.getTariffName(), e);
            } catch (Throwable e) {
                joblogger.error(e.getMessage(), e);
                joberrlogger.error("文件：【{}】执行异常；异常原因：{}", fileName, e.getMessage(), e);
            }
        }
    }

    /**
     * 处理未备案资费方案数据重置处理
     *
     * @param planIds            未备案资费方案id集合
     * @param linkMap            关联map
     * @param type               资费类型
     * @param tariffProvinceCode 资费省份编码
     * @throws RuntimeException 异常
     */
    private void handleunreportedDataInit(List<String> planIds, Map<String, String> linkMap, String type,
                                          String tariffProvinceCode, String auditDate) throws RuntimeException {
        joblogger.info(
                "[TariffGetAuditResultServiceExecutor] -> handleunreportedDataInit 开始处理未备案资费方案数据重置处理,未备案资费方案数量:{}",
                planIds.size());

        // 创建CompletableFuture任务列表
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (String planId : planIds) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    initPlanRecord(planId, linkMap, type, tariffProvinceCode, auditDate);
                } catch (Throwable e) {
                    joblogger.error(e.getMessage(), e);
                    throw new RuntimeException(e);
                }
            }, ThreadPoolManager.getExecutorService());

            futures.add(future);
        }

        // 等待所有任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            joblogger.error("处理未备案资费方案数据重置失败", e);
        }
    }

    /**
     * 初始化资费方案记录
     *
     * @param unreportedTariffPlanId 未备案资费方案id
     * @param linkMap                关联map
     * @param type                   资费类型
     * @param tariffProvinceCode     资费省份编码
     * @throws SQLException 异常
     */
    private void initPlanRecord(String unreportedTariffPlanId, Map<String, String> linkMap, String type,
                                String tariffProvinceCode, String auditDate) throws SQLException {
        EasyQuery query = QueryFactory.getWriteQuery();
        JSONObject planRecordJson = query.queryForRow(
                "select * from " + Constants.getBusiSchema() + ".xty_tariff_plan_record where ID = ?",
                new Object[]{unreportedTariffPlanId}, new JSONMapperImpl());
        EasyRecord record = new EasyRecord(Constants.getBusiSchema() + ".xty_tariff_audit", "ID");
        record.setPrimaryValues(linkMap.get(unreportedTariffPlanId));
        Map<String, String> audit = query.findById(record);
        if (audit == null) {
            joblogger.error("处理资费方案数据重置处理执行失败，关联xty_tariff_audit表未入库--planId={},type={}", unreportedTariffPlanId, type);
            return;
        }
        String provinceCode = audit.get("PROVINCE_CODE");
        String ent = audit.get("ENT");
        String tariffName = audit.get("TARIFF_NAME");

        statPlanRecord(unreportedTariffPlanId, type, tariffProvinceCode, planRecordJson, audit, provinceCode,
                ent, tariffName, auditDate);

    }

    /**
     * 统计资费方案记录
     *
     * @param unreportedTariffPlanId 未备案资费方案id
     * @param type                   资费类型
     * @param tariffProvinceCode     资费省份编码
     * @param planRecordJson         资费方案记录
     * @param audit                  稽核记录
     * @param provinceCode           省份编码
     * @param ent                    企业编码
     * @param tariffName             资费名称
     * @throws SQLException 异常
     */
    private void statPlanRecord(String unreportedTariffPlanId, String type, String tariffProvinceCode,
                                JSONObject planRecordJson, Map<String, String> audit, String provinceCode, String ent,
                                String tariffName, String auditDate) throws SQLException {
        String schema = Constants.getBusiSchema();
        EasyQuery query = QueryFactory.getWriteQuery();
        // 获取订单数量和关联区域
        String sql = "select count(1) from " +
                schema + ".xty_tariff_audit t " +
                " where t.plan_record_id=? ";
        boolean exist = query.queryForExist(sql, new Object[]{unreportedTariffPlanId});
        if(!exist) {
            String delSql = "delete from " + Constants.getBusiSchema() + ".xty_tariff_plan_record where id=?";
            QueryFactory.getWriteQuery().execute(delSql, new Object[]{unreportedTariffPlanId});
            return;
        }
        int orderNumber = 0;
        int areaCount = 0;
        List<String> relatedAreas = new ArrayList<>();
        String relatedArea = CollUtil.join(relatedAreas, ",");

        JSONObject auditDateJson = RedissonUtil.get("tariff:audit:date:"+ unreportedTariffPlanId);
        if(auditDateJson==null) {
            // 是否关联 首次末次出现日期
            String sql1 = "SELECT min(t.audit_date) as first_time,max(t.audit_date) as last_time "
                    +
                    "FROM " + Constants.getBusiSchema() + ".xty_tariff_audit t " +
                    " WHERE t.PLAN_RECORD_ID=?";
            auditDateJson = query.queryForRow(sql1, new Object[]{unreportedTariffPlanId}, new JSONMapperImpl());
        }


        String firstTime = auditDateJson.getString("FIRST_TIME");
        String lastTime = auditDateJson.getString("LAST_TIME");
        if (StringUtils.isBlank(firstTime) || DateUtil.compareDate(auditDate, firstTime, "yyyy-MM-dd") < 0) {
            firstTime = auditDate;
        }
        if (StringUtils.isBlank(lastTime) || DateUtil.compareDate(auditDate, lastTime, "yyyy-MM-dd") > 0) {
            lastTime = auditDate;
        }

        auditDateJson.put("FIRST_TIME", firstTime);
        auditDateJson.put("LAST_TIME", lastTime);
        RedissonUtil.set("tariff:audit:date:"+ unreportedTariffPlanId, auditDateJson);


        EasyRecord planRecord = new EasyRecord(schema + ".xty_tariff_plan_record", "ID");
        planRecord.setPrimaryValues(unreportedTariffPlanId);
        planRecord.set("TARIFF_TYPE", type);
        String now = EasyDate.getCurrentTimeStampString();
        if (planRecordJson != null) { // 修改
            planRecord.set("TARIFF_TYPE", planRecordJson.getString("TARIFF_TYPE"));
            // 如果是更新操作 则需要对比除此出现时间和最后一次出现时间
            // 如果当前获取到的初次出现时间早于表中的时间则进行更新
            // 同理如果当前获取到的最后出现时间晚于表中的时间则进行更新；否则不更新
            planRecord.set("LAST_TIME", lastTime);
            planRecord.set("FIRST_TIME", firstTime);
            planRecord.set("ORDER_COUNTS", orderNumber)
                    .set("LAST_UPT_TIME", now)
                    .set("RELATED_AREA", relatedArea)
                    .set("AREA_COUNT", areaCount);
            query.update(planRecord);

        } else { // 新增
            String reportNo = CACHE.get(unreportedTariffPlanId, k -> "0");
            planRecord.set("TARIFF_NAME", tariffName)
                    .set("TARIFF_LIKE_NAME", audit.get("TARIFF_LIKE_NAME"))
                    .set("TARIFF_NO", reportNo)
                    .set("PROVINCE_CODE", provinceCode)
                    .set("TARIFF_PROVINCE_CODE", tariffProvinceCode)
                    .set("PROVINCE_NAME", XtyTariffContainer.getProvinceName(provinceCode))
                    .set("ENT", ent)
                    .set("ENT_NAME", audit.get("ENT_NAME"))
                    .set("FIRST_TIME", firstTime)
                    .set("LAST_TIME", lastTime)
                    .set("ORDER_COUNTS", orderNumber)
                    .set("TARIFF_TYPE", type)
                    .set("LAST_UPT_TIME", now)
                    .set("CREATE_TIME", now)
                    .set("CREATE_USER", "系统")
                    .set("RELATED_AREA", relatedArea)
                    .set("AREA_COUNT", areaCount);
            query.save(planRecord);
        }
    }

    /**
     * 未备案资费稽核
     *
     * @date 2024/11/27 9:23
     * @param redisKey                 缓存key
     * @param auditDate                稽核日期
     * @param dataJson                 稽核结果json
     * @param fileName                 文件名
     * @param tariffAuditValues        稽核结果入库对象
     * @param createTime               创建时间
     * @param tariffOrderAreaValues    订购区域入库对象
     * @param tariffOrderUserValues    订购用户入库对象
     * @param unreportedTariffPlanIds  未备案资费方案id集合
     * @param linkMap                  关联map
     * @param auditRecord              稽核结果入库对象
     * @param tariffProvinceCode       省份编码
     * @throws Exception 异常
     */
    private void unreportTariffAudit(String redisKey, String auditDate,
                                     JSONObject dataJson, String fileName, List<Object[]> tariffAuditValues,
                                     String createTime, List<Object[]> tariffOrderAreaValues, List<Object[]> tariffOrderUserValues,
                                     List<String> unreportedTariffPlanIds,
                                     Map<String, String> linkMap,
                                     EasyRecord auditRecord, String tariffProvinceCode, String monthId, String dateId) throws Exception {
        String name = fileName.split("-")[0];
        if (Constants.ENT_TYPE_005.equals(name)) {
            name = Constants.ENT_TYPE_004;
        }
        // 企业(冗余)
        String ent = name.substring(name.length() - 1);
        if (Constants.ENT_TYPE_005.equals(ent))
            ent = Constants.ENT_TYPE_004;
        String entName = BusiUtil.getEntByEntCode(ent);
        auditRecord.put("ENT_NAME", entName);
        auditRecord.put("ENT", ent);
        auditRecord.put("PROVINCE_CODE", getProvinceCode(fileName));
        auditRecord.put("PROVINCE_NAME", BusiUtil.getProvinceNameBySimplePinyin(fileName.substring(0, 2)));
        auditRecord.put("TARIFF_AUDIT_DATE", auditDate.replaceAll("-", ""));
        auditRecord.put("TARIFF_AUDIT_MONTH", monthId);
        auditRecord.put("FILE_NAME", fileName);

        // 未订购明细
        JSONArray unreportedArr = dataJson.getJSONArray("unreported");
        if (unreportedArr != null && !unreportedArr.isEmpty()) {
            joblogger.info("[TariffJobService] -> auditHandle {} 未订购数量:{}", fileName, unreportedArr.size());
            String type = "1";
            handleNoReportedInfo(unreportedArr, tariffAuditValues, createTime, auditDate, tariffOrderAreaValues,
                    fileName, tariffOrderUserValues, redisKey, unreportedTariffPlanIds, type, linkMap, auditRecord,
                    tariffProvinceCode, monthId, dateId);
        } else {
            joblogger.info("[TariffJobService] -> auditHandle json文件:{}不存在未订购信息", fileName);
        }
        // 执行批处理
        CompletableFuture<Object> objectCompletableFuture = CompletableFuture.supplyAsync(() -> {
            executeSaveAuditBatch(tariffAuditValues, insertTariffAuditSql.getSQL(), fileName, "XTY_TARIFF_AUDIT");
            return null;
        }, ThreadPoolManager.getExecutorService()).exceptionally(e -> {
            joblogger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        });
        CompletableFuture<Object> objectCompletableFuture1 = CompletableFuture.supplyAsync(() -> {
            executeSaveAuditBatch(tariffOrderAreaValues, insertTariffOrderAreaSql.getSQL(), fileName, "XTY_TARIFF_ORDER_AREA");
            return null;
        }, ThreadPoolManager.getExecutorService()).exceptionally(e -> {
            joblogger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        });
        /* CompletableFuture<Object> objectCompletableFuture2 = CompletableFuture.supplyAsync(() -> {
            executeSaveAuditBatch(tariffOrderUserValues, insertTariffOrderUsersql.getSQL(), fileName, "XTY_TARIFF_ORDER_USER");
            return null;
        }, ThreadPoolManager.getExecutorService(Constants.TARIFF_AUDIT_THREAD_POOL_NAME)).exceptionally(e -> {
            joblogger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }); */
        CompletableFuture.allOf(objectCompletableFuture, objectCompletableFuture1).join();
        // 最后等1秒让CPU摸会鱼
        //CommonUtil.sleep(1);
    }

    /**
     * 执行批量操作
     * @param tariffAuditValues 稽核结果入库对象
     * @param sql 入库sql
     * @param fileName 文件名
     */
    private void executeSaveAuditBatch(List<Object[]> tariffAuditValues, String sql, String fileName, String objName) {
        int BATCH_SIZE = 5000;
        if (!tariffAuditValues.isEmpty()) {
            Connection connection = null;
            PreparedStatement pstmt = null;
            try {
                connection = QueryFactory.getWriteQuery().getConnection();
                connection.setAutoCommit(false);
                pstmt = connection.prepareStatement(sql);
                pstmt.setQueryTimeout(300);

                int totalSize = tariffAuditValues.size();

                for (int i = 0; i < totalSize; i++) {
                    Object[] params = tariffAuditValues.get(i);
                    for (int j = 0; j < params.length; j++) {
                        pstmt.setObject(j + 1, params[j]);
                    }
                    pstmt.addBatch();

                    // 每5000条执行一次
                    if ((i + 1) % BATCH_SIZE == 0 || i == totalSize - 1) {
                        pstmt.executeBatch();
                        pstmt.clearBatch();
                        //joblogger.info("[{}]成功执行批量操作,总数:{},当前处理到第{}条", fileName, totalSize, i + 1);
                        connection.commit(); // 提交事务
                    }
                }
                joblogger.info("[{}] 成功执行批量操作[{}], 总数:{}", fileName, objName, totalSize);
            } catch (Exception e) {
                handleException(e, fileName, connection);
            } finally {
                closeResources(pstmt, connection);
            }
        }
    }

    private void handleException(Exception e, String fileName, Connection connection) {
        if (connection != null) {
            try {
                connection.rollback();
            } catch (SQLException e1) {
                joblogger.error(e1.getMessage(), e1);
            }
        }
        joblogger.error("文件【{}】 执行批量入库异常，异常原因：{}", fileName, e.getMessage());
        throw new RuntimeException(e);
    }

    private void closeResources(PreparedStatement pstmt, Connection connection) {
        if (pstmt != null) {
            try {
                pstmt.close();
            } catch (Exception e) {
                joblogger.error(e.getMessage(), e);
            }
        }
        if (connection != null) {
            try {
                connection.close();
            } catch (Exception e) {
                joblogger.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 续订资费稽核
     *
     * @date 2024/11/27 9:23
     * @param redisKey                 缓存key
     * @param auditDate                稽核日期
     * @param dataJson                 稽核结果json
     * @param fileName                 文件名
     * @param tariffAuditValues        稽核结果入库对象
     * @param createTime               创建时间
     * @param tariffOrderAreaValues    订购区域入库对象
     * @param tariffOrderUserValues    订购用户入库对象
     * @param offlineTariffPlanIds     续订资费方案id集合
     * @param linkMap                  关联map
     * @param auditRecord              稽核结果入库对象
     * @param tariffProvinceCode       省份编码
     * @throws Exception 异常
     */
    private void offlinedTariffAudit(String redisKey, String auditDate,
                                     JSONObject dataJson, String fileName, List<Object[]> tariffAuditValues,
                                     String createTime, List<Object[]> tariffOrderAreaValues, List<Object[]> tariffOrderUserValues,
                                     List<String> offlineTariffPlanIds,
                                     Map<String, String> linkMap,
                                     EasyRecord auditRecord, String tariffProvinceCode, String monthId, String dateId) throws Exception {
        // 未订购明细
        JSONArray unreportedArr = dataJson.getJSONArray("offlined");
        if (unreportedArr != null && !unreportedArr.isEmpty()) {
            joblogger.info("[TariffJobService] -> auditHandle {} 未订购数量:{}", fileName, unreportedArr.size());
            String type = "2";
            handleNoReportedInfo(unreportedArr, tariffAuditValues, createTime, auditDate, tariffOrderAreaValues,
                    fileName, tariffOrderUserValues,
                    redisKey, offlineTariffPlanIds, type, linkMap, auditRecord, tariffProvinceCode, monthId, dateId);
        } else {
            joblogger.info("[TariffJobService] -> auditHandle json文件:{}不存在未订购信息", fileName);
        }

        // 执行批处理
        CompletableFuture<Object> objectCompletableFuture = CompletableFuture.supplyAsync(() -> {
            executeSaveAuditBatch(tariffAuditValues, insertTariffAuditSql.getSQL(), fileName, "XTY_TARIFF_AUDIT");
            return null;
        }, ThreadPoolManager.getExecutorService()).exceptionally(e -> {
            joblogger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        });
        CompletableFuture<Object> objectCompletableFuture1 = CompletableFuture.supplyAsync(() -> {
            executeSaveAuditBatch(tariffOrderAreaValues, insertTariffOrderAreaSql.getSQL(), fileName, "XTY_TARIFF_ORDER_AREA");
            return null;
        }, ThreadPoolManager.getExecutorService()).exceptionally(e -> {
            joblogger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        });
        /* CompletableFuture<Object> objectCompletableFuture2 = CompletableFuture.supplyAsync(() -> {
            executeSaveAuditBatch(tariffOrderUserValues, insertTariffOrderUsersql.getSQL(), fileName, "XTY_TARIFF_ORDER_USER");
            return null;
        }, ThreadPoolManager.getExecutorService(Constants.TARIFF_AUDIT_THREAD_POOL_NAME)).exceptionally(e -> {
            joblogger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }); */
        CompletableFuture.allOf(objectCompletableFuture, objectCompletableFuture1).join();

        // 最后等1秒让CPU摸会鱼
        //CommonUtil.sleep(1);
    }


    private void markAuditFileErrLog(String provinceCode, String provinceName, String tariffProvinceCode, String ent, String entName,String fileName, String tariffName, String auditDate, String errMsg) {
        EasyRecord record = new EasyRecord(Constants.getBusiTable("XTY_TARIFF_AUDIT_ERROR_LOG"), "ID");
        record.setPrimaryValues(IdUtil.getSnowflakeNextId());
        JSONObject json = new JSONObject();
        json.put("PROVINCE_CODE", provinceCode);
        json.put("TARIFF_PROVINCE_CODE", tariffProvinceCode);
        json.put("PROVINCE_NAME", provinceName);
        json.put("ENT", ent);
        json.put("ENT_NAME", entName);
        json.put("AUDIT_FILE", fileName);
        json.put("TARIFF_NAME", tariffName);
        json.put("AUDIT_DATE", auditDate);
        json.put("ERROR_MSG", errMsg);
        json.put("CREATE_TIME", EasyDate.getCurrentTimeStampString());
        joberrlogger.info("未订购信息解析异常，异常原因：{}", json);
        try {
            record.setColumns(json);
            QueryFactory.getWriteQuery().save(record);
        } catch (Exception e) {
            joblogger.error(e.getMessage(), e);
        }
    }


    /**
     * 处理未订购信息
     *
     * @since 2024/11/27 9:23
     * @param unreportedArr         未订购json数组
     * @param tariffAuditValues     稽核结果入库对象
     * @param createTime            创建时间
     * @param auditDate             稽核日期
     * @param tariffOrderAreaValues 订购区域入库对象
     * @param fileName              文件名
     * @param tariffOrderUserValues 订购用户入库对象
     * @param redisKey              缓存key
     * @param tariffPlanIds         资费方案id集合
     * @param type                  类型
     * @param linkMap               关联map
     * @param auditRecord           稽核结果入库对象
     * @param tariffProvinceCode    省份编码
     * @throws Exception 异常
     */
    private void handleNoReportedInfo(JSONArray unreportedArr, List<Object[]> tariffAuditValues,
                                      String createTime, String auditDate, List<Object[]> tariffOrderAreaValues, String fileName,
                                      List<Object[]> tariffOrderUserValues, String redisKey, List<String> tariffPlanIds, String type,
                                      Map<String, String> linkMap, EasyRecord auditRecord, String tariffProvinceCode, String monthId, String dateId) throws Exception {
        String auditDateInt = auditDate.replaceAll("-", "");
        String name = fileName.split("-")[0];
        // 稽核数据结果入库的时候，如果是005的数据，需要转换成004
        if (Constants.ENT_TYPE_005.equals(name)) {
            name = Constants.ENT_TYPE_004;
        }
        // 企业(冗余)
        String ent = name.substring(name.length() - 1);
        if (Constants.ENT_TYPE_005.equals(ent))
            ent = Constants.ENT_TYPE_004;
        // 企业名称(冗余)
        String entName = BusiUtil.getEntByEntCode(ent);

        int orderNum_All = 0;
        int areaNum_All = 0;

        // 获取当前稽核文件的所属省份
        String provinceCode = getProvinceCode(fileName);
        String provinceName = XtyTariffContainer.getProvinceName(provinceCode);

        for (int i = 0; i < unreportedArr.size(); i++) {

            JSONObject reportedJson = unreportedArr.getJSONObject(i);
            // 备案号
            String reportNo = reportedJson.getString("report_no");
            if (StringUtils.equals(type, "1") && StringUtils.isBlank(reportNo)) {
                reportNo = "0";
            }
            // 订单总量
            String orderNum = reportedJson.getString("total");

            orderNum_All = orderNum_All + reportedJson.getIntValue("total");
            // 销售信息
            JSONArray saledList = reportedJson.getJSONArray("saled_list");
            String tariffAuditId = IdUtil.getSnowflakeNextIdStr();

            // 资费记录ID
            String tariffRecordId = "0";

            // 是否存在备案(冗余)
            String existReport = "N";
            // 资费名称
            String tariffName = StringUtils.trimToEmpty(reportedJson.getString("name"));
            if(StringUtils.length(tariffName) > 200) {
                markAuditFileErrLog(provinceCode, provinceName, tariffProvinceCode, ent, entName, fileName, tariffName, auditDate, "资费名称长度不能超过200个字符");
                throw new TariffAuditException("资费名称长度不能超过200个字符", tariffName, fileName);
            }
            String errReportNo = reportedJson.getString("error_report_no");
            //防止错误编码过长如果长度超过200则进行截断 20241129 14:45:45
            if (StringUtils.isNotBlank(errReportNo) && StringUtils.length(errReportNo) > 200) {
                errReportNo = StringUtils.substring(errReportNo, 0, 200);
            }
            String tariffLikeName = reportedJson.getString("like_name");
            // 来源说明
            String source = "sftp-" + redisKey;

            String singleKey = reportNo;
            if(StringUtils.isBlank(singleKey)) {
                singleKey = tariffName;
            }
            // 资费方案ID
            String tariffPlanRecordId = SecureUtil.sha256(singleKey+ent+tariffProvinceCode+type);
            // XtyTariffContainer.cacheReportNo(tariffPlanRecordId, reportNo);

            EasyCalendar easyCalendar = EasyCalendar.newInstance(auditDate, "yyyy-MM-dd");
            int dateInt = easyCalendar.getDateInt();
            String fullMonth = easyCalendar.getFullMonth();

            linkMap.put(tariffPlanRecordId, tariffAuditId);
            CACHE.put(tariffPlanRecordId, reportNo);

            List<Object[]> tariffOrderAreaValuesSingle = new ArrayList<>();
            List<Object[]> tariffOrderUserValuesSingle = new ArrayList<>();
            if (saledList != null && !saledList.isEmpty()) {
                // 订购汇总来源 1-稽核 2-运营商上报
                String tariffOrderAreaSource = "1";
                for (int j = 0; j < saledList.size(); j++) {
                    areaNum_All = areaNum_All + saledList.getJSONObject(j).getIntValue("sale_count");

                    analyzeTariffArea(createTime, tariffOrderAreaValuesSingle, tariffOrderUserValuesSingle, saledList,
                            j, tariffAuditId,
                            tariffOrderAreaSource, auditDateInt, tariffRecordId, reportNo, source, existReport,
                            tariffName, ent, entName, type, tariffProvinceCode, provinceCode, provinceName, auditDate, monthId, dateId);

                }
            }
            if (!tariffOrderAreaValuesSingle.isEmpty()) {
                tariffOrderAreaValues.addAll(tariffOrderAreaValuesSingle);
                tariffOrderUserValues.addAll(tariffOrderUserValuesSingle);
                tariffAuditValues.add(new Object[]{
                        tariffAuditId, tariffRecordId, ent, entName, existReport, reportNo, tariffName, auditDate,
                        orderNum, createTime, source, tariffLikeName, provinceCode, tariffPlanRecordId, type, dateInt,
                        fullMonth, tariffProvinceCode, errReportNo
                });
                tariffPlanIds.add(tariffPlanRecordId);
            }
        }
        if ("1".equals(type)) { // 未备案
            auditRecord.put("UNREPORTED_TARIFF_COUNT", orderNum_All);
            auditRecord.put("UNREPORTED_TARIFF_ORDER_COUNT", areaNum_All);
        } else {// 续订
            auditRecord.put("HISREPORTED_TARIFF_COUNT", orderNum_All);
            auditRecord.put("HISREPORTED_TARIFF_ORDER_COUNT", areaNum_All);
        }
    }

    /**
     * 解析资费地区信息
     *
     * @since 2024/11/27 9:23
     * @param createTime            创建时间
     * @param tariffOrderAreaValues 订购区域入库对象
     * @param tariffOrderUserValues 订购用户入库对象
     * @param saledList             销售列表
     * @param j                     索引
     * @param tariffAuditId         稽核id
     * @param tariffOrderAreaSource 订购区域来源
     * @param auditDateInt          稽核日期
     * @param tariffRecordId        资费记录id
     * @param reportNo              备案号
     * @param source                来源
     * @param existReport           是否存在备案
     * @param tariffName            资费名称
     * @param ent                   企业
     * @param entName               企业名称
     * @param type                  类型
     * @throws Exception 异常
     */
    private void analyzeTariffArea(String createTime, List<Object[]> tariffOrderAreaValues,
                                   List<Object[]> tariffOrderUserValues, JSONArray saledList, int j,
                                   String tariffAuditId, String tariffOrderAreaSource, String auditDateInt, String tariffRecordId, String reportNo,
                                   String source, String existReport, String tariffName, String ent, String entName, String type,
                                   String tariffProvinceCode, String provinceCode, String provinceName, String auditDate, String monthId, String dateId)
            throws Exception {
        String tariffOrderAreaId = IdUtil.getSnowflakeNextIdStr();
        JSONObject saledJson = saledList.getJSONObject(j);
        // 地区
        String area = saledJson.getString("area");
        if (StringUtils.equals("3360", area))
            return;
        // 销售数量
        String tariffOrderAreaOrderNum = saledJson.getString("sale_count");
        String areaName = "";
        JSONObject areaJson = RedissonUtil.hget("TARIFF_PROVINCE_AREA", area);
        if (areaJson != null) {
            areaName = areaJson.getString("TARIFF_AREA_NAME");
        }
        tariffOrderAreaValues.add(new Object[]{
                tariffOrderAreaId, tariffAuditId, tariffOrderAreaSource, area, areaName, tariffOrderAreaOrderNum,
                createTime, provinceCode, provinceName, auditDateInt, type, ent, tariffProvinceCode
        });

        // 获取详细信息
        /* JSONArray detailsArr = saledJson.getJSONArray("details");
        if (detailsArr != null && !detailsArr.isEmpty()) {
            for (int k = 0; k < detailsArr.size(); k++) {
                analyzeTariffUserData(createTime, tariffOrderUserValues, detailsArr, k, tariffRecordId, reportNo, area,
                        areaName, source,
                        tariffAuditId, tariffOrderAreaId, provinceCode, provinceName, existReport, tariffName, ent,
                        entName, type, tariffProvinceCode, auditDate, monthId, dateId);
            }
        } */
    }

    /**
     * 解析资费订购信息
     *
     * @since 2024/11/27 9:23
     * @param createTime            创建时间
     * @param tariffOrderUserValues 订购用户入库对象
     * @param detailsArr            销售列表
     * @param k                     索引
     * @param tariffRecordId        资费记录id
     * @param reportNo              备案号
     * @param area                  地区
     * @param areaName              地区名称
     * @param source                来源
     * @param tariffAuditId         稽核id
     * @param tariffOrderAreaId     订购区域id
     * @param provinceCode          省份编码
     * @param provinceName          省份名称
     * @param existReport           是否存在备案
     * @param tariffName            资费名称
     * @param ent                   企业
     * @param entName               企业名称
     * @param type                  类型
     */
    private void analyzeTariffUserData(String createTime, List<Object[]> tariffOrderUserValues, JSONArray detailsArr,
                                       int k,
                                       String tariffRecordId, String reportNo, String area, String areaName, String source, String tariffAuditId,
                                       String tariffOrderAreaId, String provinceCode, String provinceName, String existReport, String tariffName,
                                       String ent, String entName, String type, String tariffProvinceCode, String auditDate, String monthId, String dateId) {
        JSONObject detailsJson = detailsArr.getJSONObject(k);

        String phonenum = detailsJson.getString("phone");
        String orderTime = detailsJson.getString("start_time");
        String orderBakup = detailsJson.getString("sms");
        String bakup = "";
        String isValid = "";

        String tariffOrderUserid = IdUtil.getSnowflakeNextIdStr();
        tariffOrderUserValues.add(new Object[]{
                tariffOrderUserid, tariffRecordId, reportNo, orderTime, area, areaName, phonenum, orderBakup,
                createTime, bakup, source, tariffAuditId, tariffOrderAreaId, isValid, provinceCode, provinceName,
                existReport, tariffName, ent, entName, type, tariffProvinceCode, auditDate, monthId, dateId
        });
    }

    /**
     * 加载省份和地区缓存信息
     */
    private void reloadProviceAndArea() {
        joblogger.info("{} 开始加载资费省份和地区缓存信息...", CommonUtil.getClassNameAndMethod(this));
        EasyQuery query = QueryFactory.getWriteQuery();
        EasySQL sql = new EasySQL();
        sql.append(" select t1.TARIFF_AREA_CODE,t1.PROVINCE_CODE,t1.TARIFF_AREA_NAME,t1.PROVINCE_NAME from "
                + Constants.getBusiSchema() + ".xty_tariff_area t1  ");

        try {
            List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

            joblogger.info("{} 需要加载资费的省份和地区缓存信息：{}", CommonUtil.getClassNameAndMethod(this),
                    list != null ? list.size() : 0);

            if (list != null && !list.isEmpty()) {
                for (JSONObject json : list) {
                    String area = json.getString("TARIFF_AREA_CODE");
                    RedissonUtil.hset("TARIFF_PROVINCE_AREA", area, json);
                }
                joblogger.info("加载资费地区和省份的缓存,数量:{}", list.size());
            }
        } catch (SQLException e) {
            joblogger.error("加载资费地区和省份的缓存异常:{}", e.getMessage(), e);
        }
        joblogger.info("{} 完成加载资费省份和地区缓存信息...", CommonUtil.getClassNameAndMethod(this));
    }

    /**
     * 删除历史数据
     *
     * @param auditDate          稽核日期
     * @param provinceCode       省份编码
     * @param tariffProvinceCode 资费省份编码
     * @param entCode            企业编码
     * @param planRecordIds      需要统计的资费方案记录id集合
     * @throws SQLException       异常
     */
    private void delOldAudit(String auditDate, String provinceCode, String tariffProvinceCode, String entCode, Set<JSONObject> planRecordIds, String dateId, String fileName, String monthId)
            throws SQLException {
        joblogger.info("[delOldAudit] 删除当前文件对应旧数据：auditDate:{}, provinceCode:{}, tariffProvinceCode:{}, entCode:{}", auditDate, provinceCode, tariffProvinceCode, entCode);
        try {
            // 1. 获取需要删除的审计ID
            List<String> auditIds = getAuditIdsToDelete(auditDate, tariffProvinceCode, provinceCode, entCode);
            if (auditIds.isEmpty()) {
                joblogger.info("没有找到需要删除的历史数据");
                return;
            }

            // 2. 批量删除关联表数据
            deleteRelatedTableData(provinceCode, entCode, tariffProvinceCode, auditDate, dateId);

            // 3. 删除方案记录表数据
            deletePlanRecordData(auditDate, entCode, provinceCode, tariffProvinceCode, planRecordIds);

            // 4. 删除审计主表数据
            deleteAuditMainData(auditDate, provinceCode, tariffProvinceCode, entCode);

            // 5. 删除ES数据
            TariffAuditProcessStorageService.getInstance().deleteOldAuditData(monthId, dateId, entCode, tariffProvinceCode, fileName);
        } catch (SQLException e) {
            joblogger.error("删除历史数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取需要删除的审计ID
     *
     * @param auditDate          稽核日期
     * @param tariffProvinceCode 资费省份编码
     * @param provinceCode       省份编码
     * @param entCode            企业编码
     * @return 审计ID集合
     * @throws SQLException 异常
     */
    private List<String> getAuditIdsToDelete(String auditDate, String tariffProvinceCode,
                                             String provinceCode, String entCode) throws SQLException {
        EasySQL queryAuditIds = new EasySQL()
                .append("select ID from " + Constants.getBusiSchema() + ".XTY_TARIFF_AUDIT")
                .append(auditDate, "where AUDIT_DATE = ?")
                .append(tariffProvinceCode, "and TARIFF_PROVINCE_CODE = ?")
                .append(provinceCode, "and PROVINCE_CODE = ?")
                .append(entCode, "and ENT = ?");
        joblogger.info("[getAuditIdsToDelete]查询待删除的审计ID, SQL: {}", queryAuditIds.toFullSql());
        try {
            EasyQuery readQuery = QueryFactory.getReadQuery();
            return readQuery.queryForList(queryAuditIds.getSQL(), queryAuditIds.getParams(),
                    new EasyRowMapper<String>() {
                        @Override
                        public String mapRow(ResultSet resultSet, int i) {
                            try {
                                return resultSet.getString("ID");
                            } catch (Exception e) {
                                joblogger.error(e.getMessage(), e);
                            }
                            return "";
                        }
                    });
        } catch (SQLException e) {
            joblogger.error("查询待删除的审计ID失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 删除关联表数据
     * @param provinceCode 省份编码
     * @param entCode 企业编码
     * @param tariffProvinceCode 资费省份编码
     * @param auditDate 稽核日期
     * @throws SQLException 异常
     */
    private void deleteRelatedTableData(String provinceCode, String entCode, String tariffProvinceCode, String auditDate, String dateId) throws SQLException {

        // 删除订单区域表数据
        CompletableFuture<Object> uCompletableFuture = CompletableFuture.supplyAsync(() -> {
            try {

                deleteWithRetryInternal(Constants.getBusiSchema() + ".XTY_TARIFF_ORDER_AREA", "DATE_ID", auditDate.replace("-", ""), provinceCode, entCode, tariffProvinceCode, auditDate);
            } catch (SQLException e) {
                joblogger.error(e.getMessage(), e);
            }
            return null;
        });
        CompletableFuture<Object> objectCompletableFuture = CompletableFuture.supplyAsync(() -> {
            // 删除订单用户表数据
            try {
                deleteWithRetryInternal(Constants.getBusiSchema() + ".XTY_TARIFF_ORDER_USER", "DATE_ID", dateId, provinceCode, entCode, tariffProvinceCode, auditDate);
            } catch (SQLException e) {
                joblogger.error(e.getMessage(), e);
            }
            return null;
        });
        CompletableFuture.allOf(uCompletableFuture, objectCompletableFuture).join();
        joblogger.info("成功删除关联表历史数据");
    }

    private void deleteAuditMainData(String auditDate, String provinceCode, String tariffProvinceCode, String entCode) throws SQLException {
        try {

            EasyQuery writeQuery = QueryFactory.getWriteQuery();
            writeQuery.setTimeout(180);

            // 删除审计主表
            EasySQL delAuditSql = new EasySQL()
                    .append("delete from " + Constants.getBusiSchema() + ".XTY_TARIFF_AUDIT")
                    .append(auditDate, "where AUDIT_DATE = ?");
            delAuditSql.append(provinceCode, "and PROVINCE_CODE = ?")
                    .append(tariffProvinceCode, "and TARIFF_PROVINCE_CODE = ?")
                    .append(entCode, "and ENT = ?");
            writeQuery.execute(delAuditSql.getSQL(), delAuditSql.getParams());

            // 删除审计结果表
            EasySQL delResultSql = new EasySQL()
                    .append("delete from " + Constants.getBusiSchema() + ".XTY_TARIFF_AUDIT_RESULT")
                    .append(auditDate.replaceAll("-", ""), "where TARIFF_AUDIT_DATE = ?");
            delResultSql.append(provinceCode, "and PROVINCE_CODE = ?")
                    .append(tariffProvinceCode, "and TARIFF_PROVINCE_CODE = ?")
                    .append(entCode, "and ENT = ?");
            writeQuery.execute(delResultSql.getSQL(), delResultSql.getParams());

            joblogger.info("成功删除审计主表和结果表数据");
        } catch (SQLException e) {
            joblogger.error("删除审计主表数据失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 删除方案记录表数据
     *
     * @param auditDate          稽核日期
     * @param entCode            企业编码
     * @param provinceCode       省份编码
     * @param tariffProvinceCode 资费省份编码
     * @param planRecordIds      需要统计的资费方案记录id集合
     * @throws SQLException 异常
     */
    private void deletePlanRecordData(String auditDate, String entCode, String provinceCode,
                                      String tariffProvinceCode, Set<JSONObject> planRecordIds) throws SQLException {
        EasySQL delPlanSql = new EasySQL()
                .append("delete from " + Constants.getBusiSchema() + ".XTY_TARIFF_PLAN_RECORD t")
                .append(auditDate, "where t.FIRST_TIME = ?")
                .append(auditDate, "and t.LAST_TIME = ?")
                .append(tariffProvinceCode, "and t.TARIFF_PROVINCE_CODE = ?")
                .append(provinceCode, "and t.PROVINCE_CODE = ?")
                .append(entCode, "and t.ENT = ?");

        delPlanSql.append("and EXISTS(")
                .append("select 1 from " + Constants.getBusiSchema() + ".XTY_TARIFF_AUDIT t1")
                .append(auditDate, "where t1.AUDIT_DATE = ?")
                .append(tariffProvinceCode, "and t1.TARIFF_PROVINCE_CODE = ?")
                .append(provinceCode, "and t1.PROVINCE_CODE = ?")
                .append(entCode, "and t1.ENT = ?")
                .append(")");
        EasyQuery writeQuery = QueryFactory.getWriteQuery();
        writeQuery.setTimeout(180);
        joblogger.info("删除方案记录表数据, SQL: {}, 参数: {}", delPlanSql.getSQL(), delPlanSql.getParams());
        writeQuery.execute(delPlanSql.getSQL(), delPlanSql.getParams());

        EasySQL reStatSql = new EasySQL()
                .append("SELECT t.ID,t.TARIFF_TYPE,t.FIRST_TIME,t.LAST_TIME from " + Constants.getBusiSchema()
                        + ".XTY_TARIFF_PLAN_RECORD t")
                .append("where t.FIRST_TIME <> t.LAST_TIME")
                .append(tariffProvinceCode, "and t.TARIFF_PROVINCE_CODE = ?")
                .append(provinceCode, "and t.PROVINCE_CODE = ?")
                .append(entCode, "and t.ENT = ?")
                .append(auditDate, "and (t.FIRST_TIME=? OR t.LAST_TIME=?)");
        planRecordIds
                .addAll(writeQuery.queryForList(reStatSql.getSQL(), reStatSql.getParams(), new JSONMapperImpl()));
    }

    /**
     * 批量删除
     * @param tableName 表名
     * @param dateColumn 日期列名
     * @param dateValue 日期值
     * @param provinceCode 省份编码
     * @param entCode 企业编码
     * @param tariffProvinceCode 资费省份编码
     * @param auditDate 稽核日期
     * @throws SQLException 异常
     */
    private void deleteWithRetryInternal(String tableName, String dateColumn, String dateValue, String provinceCode, String entCode, String tariffProvinceCode, String auditDate) throws SQLException {

        Object[] params = {provinceCode, entCode, tariffProvinceCode, dateValue, provinceCode, tariffProvinceCode, entCode, auditDate};
        String sql = "delete from " + tableName + " t where t.PROVINCE_CODE=? and t.ENT=? AND t.TARIFF_PROVINCE_CODE=? and t." + dateColumn + "=? " +
                "AND EXISTS (select 1 from " + Constants.getBusiTable("XTY_TARIFF_AUDIT") + " t1 where t.TARIFF_AUDIT_ID=t1.ID and t1.PROVINCE_CODE=? " +
                "and t1.TARIFF_PROVINCE_CODE=? and t1.ENT=? and t1.AUDIT_DATE=?" +
                ") limit 500";
        int result = 0;
        Connection connection = null;
        PreparedStatement pstmt = null;
        int count = 0;
        try {
            connection = QueryFactory.getWriteQuery().getConnection();
            pstmt = connection.prepareStatement(sql);
            connection.setAutoCommit(false); // 开启事务
            pstmt.setQueryTimeout(300);
            do {
                for (int i = 0; i < params.length; i++) {
                    pstmt.setObject(i + 1, params[i]);
                }
                result = pstmt.executeUpdate();
                count += result;

                if (count % 5000 == 0) { // 每5000条提交一次事务
                    joblogger.info("[deleteWithRetryInternal] delete {} from db {} counts", tableName, count);
                    connection.commit();
                }
            } while (result > 0);

            if (count % 5000 != 0) { // 提交剩余的事务
                connection.commit();
            }
            joblogger.info("[deleteWithRetryInternal] delete {} from db {} counts", tableName, count);
        } catch (Exception e) {
            try {
                if (connection != null) {
                    connection.rollback(); // 回滚事务
                }
            } catch (SQLException ex) {
                joblogger.error("Rollback failed: {}", ex.getMessage(), ex);
            }
            joblogger.error(e.getMessage(), e);
        } finally {
            if (pstmt != null) {
                try {
                    pstmt.close();
                } catch (Exception e) {
                    joblogger.error(e.getMessage(), e);
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (Exception e) {
                    joblogger.error(e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 获取资费稽核文件所属省份编码
     *
     * @param fileName 文件名
     */
    private String getProvinceCode(String fileName) {
        String tariffProvinceCode = fileName.substring(0, 2);
        return XtyTariffContainer.getProviceId(tariffProvinceCode);
    }

    /**
     * 获取稽核未备案数据的方案ID
     *
     * @param traiffName   资费名称
     * @param ent          企业
     * @param provinceCode 省份编码
     * @return 资费方案ID
     */
    private String getTariffPlanRecordId(String traiffName, String ent, String provinceCode, String tariffType) {
        return XtyTariffContainer.getTariffPlanId(traiffName, ent, provinceCode, tariffType);
    }
}
