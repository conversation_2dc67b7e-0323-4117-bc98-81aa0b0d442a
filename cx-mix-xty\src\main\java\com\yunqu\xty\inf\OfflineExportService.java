package com.yunqu.xty.inf;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.yq.busi.common.model.RoleModel;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.FileUtil;
import com.yunqu.xty.base.CommonLogger;
import com.yunqu.xty.base.Constants;
import com.yunqu.xty.base.QueryFactory;
import com.yunqu.xty.model.DatApiParamModel;
import com.yunqu.xty.model.XtyDatFldDefinitions;
import com.yunqu.xty.model.XtyDatFldDefinitionsPojo;
import com.yunqu.xty.thread.ThreadMgr;
import com.yunqu.xty.utils.*;
import com.yunqu.xty.utils.excel.CellStyleUtil;
import com.yunqu.xty.utils.excel.ExcelExport;
import com.yunqu.xty.utils.excel.ExcelExportDataHandle;
import com.yunqu.xty.utils.excel.impl.ExcelExportDataHandleImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.log.JDBCLogger;
import org.easitline.common.utils.string.StringUtils;
import org.springframework.util.Assert;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: cc-xty
 * @ClassName OfflineExportService
 * @description:
 * @author: zhibine.chen
 * @create: 2024-09-02 15 01
 * @Version 1.0
 **/
public class OfflineExportService extends IService {

    private final Logger logger = CommonLogger.getLogger("export");

    public final static String SERVICE_ID = "XTY_OFFLINE_EXPORT_SERVICE";

    @Override
    public JSONObject invoke(JSONObject jsonObject) throws ServiceException {
        return exportData(jsonObject);
    }

    /**
     * 导出列表数据
     * @param jsonObject
     * @return
     */
    public JSONObject exportData(JSONObject jsonObject) {
        logger.info("exportData start", null);
        JSONObject result = new JSONObject();
        String name = jsonObject.getString("name");
        String entId = jsonObject.getString("entId");
        String busiOrderId = jsonObject.getString("busiOrderId");
        String schema = jsonObject.getString("schema");
        String id = jsonObject.getString("id");
        String realPath = getRealPath(jsonObject.getString("fileDir"), name);
        JSONObject param = jsonObject.getJSONObject("expParam");
        UserModel user = JSONObject.parseObject(param.getString("user"), UserModel.class);

        if (param == null) {
            result.put("RespCode", GWConstants.RET_CODE_NOT_DATA);
            result.put("respDesc", "参数错误");
            return result;
        }

        String resId = param.getString("resId");
        String modelCode = param.getString("modelCode");
        long startMs = System.currentTimeMillis();

        try {
            logger.info("exportData user:" + JSONObject.toJSONString(user), null);
            Assert.notNull(modelCode, "列表业务模型不能为空！");

            EasyQuery query = QueryFactory.getWriteQuery();
            JSONObject dataModel = ConfigUtil.getDataModel(modelCode, query);
            if (dataModel == null) {
                throw new ServiceException(500, "数据模型配置错误");
            }

            String dataModelParams = param.getString("dataModelParams");
            String orderIds = param.getString("orderIds");
            String showFields = param.getString("fields");
            List<XtyDatFldDefinitions> definitions = StringUtils.isNotBlank(showFields) ? JSONArray.parseArray(showFields, XtyDatFldDefinitions.class) : null;
            List<DatApiParamModel> datApiParamModels = JSONArray.parseArray(dataModelParams, DatApiParamModel.class);

            JSONObject listConfigFields = ConfigUtil.getListConfigFields(modelCode, user, query);
            String searchs = listConfigFields.getString("searchs");
            String shows = listConfigFields.getString("shows");
            String cacheShow = JSONObject.toJSONString(JSONArray.parseArray(shows, XtyDatFldDefinitions.class)
                    .stream().filter(item -> !Constants.MAGIC_01.equals(JSONPath.eval(item.getComponentPropConfig(), "$.encrypt")) || "ENT_DEPT_NAME".equals(item.getFieldName()) || "lt-order".equals(modelCode)).collect(Collectors.toList()));
            List<XtyDatFldDefinitions> cacheFields = StringUtils.isNotBlank(cacheShow) ? JSONArray.parseArray(cacheShow, XtyDatFldDefinitions.class) : new ArrayList<>();
            shows = (definitions != null && definitions.size() > 0 && definitions.size() != cacheFields.size()) ? showFields : shows;
            String alls = listConfigFields.getString("alls");
            List<XtyDatFldDefinitions> searchFields = JSONArray.parseArray(searchs, XtyDatFldDefinitions.class);

            if (datApiParamModels != null && !datApiParamModels.isEmpty()) {
                datApiParamModels.forEach(item -> {
                    if (item.getParamName().equals("NODE_ID")) {
                        XtyDatFldDefinitions datFldDefinitions = new XtyDatFldDefinitions();
                        datFldDefinitions.setFieldName("NODE_ID");
                        item.setOption("01");
                        if (searchFields != null) {
                            searchFields.add(datFldDefinitions);
                        }
                    }
                });
            }

            if (StringUtils.isNotBlank(shows)) {
                List<XtyDatFldDefinitions> xtyDatFldDefinitions = filterField(user, JSONArray.parseArray(shows, XtyDatFldDefinitions.class),modelCode);
                shows = JSONObject.toJSONString(xtyDatFldDefinitions);
            }

            if (Constants.MAGIC_01.equals(Constants.getXtyIsCustomExport()) && StringUtils.equalsAny(modelCode,Constants.getSplitScope())) {
                logger.info("定制优化导出...");
                Map<String, DatApiParamModel> paramModelMap = datApiParamModels.stream()
                        .collect(Collectors.toMap(DatApiParamModel::getParamName, item -> item));
                DatApiParamModel appealTime = paramModelMap.get("APPEAL_TIME");
                if (Objects.nonNull(appealTime) && appealTime.getParamValue() instanceof JSONArray) {
                    JSONArray paramValue = (JSONArray) appealTime.getParamValue();
                    if (paramValue.size() == 2) {
                        result.put("count", customExport(dataModel,datApiParamModels,user,JSONArray.parseArray(alls, XtyDatFldDefinitions.class)
                                ,searchFields,JSONArray.parseArray(shows, XtyDatFldDefinitions.class),resId,orderIds,realPath,name,paramModelMap));
                        result.put("fileRelativeUrl", realPath);
                        result.put("respCode", GWConstants.RET_CODE_SUCCESS);
                        result.put("respDesc", "导出成功");
                        return result;
                    }
                }

            }

            EasySQL sql = BuildSqlUtils.generateSql(resId, dataModel.getString("viewSql"), datApiParamModels, query.getTypes().name(),
                    searchFields,
                    JSONArray.parseArray(shows, XtyDatFldDefinitions.class),
                    JSONArray.parseArray(alls, XtyDatFldDefinitions.class), user, !StringUtils.equalsAny(modelCode, Constants.getListNotAuth()));

            if (StringUtils.isNotBlank(orderIds)) {
                sql.appendIn(JSONArray.parseArray(orderIds, String.class).toArray(new String[]{}), " and ID ");
            }

            logger.info(modelCode + " list sql:" + sql.getFullSq(), null);

            if (StringUtils.isNotBlank(shows)) {
                List<String> headers = new ArrayList<>();
                List<String> fields = new ArrayList<>();
                Map<String, String> dictMap = new HashMap<>();
                Map<String, JSONObject> treeMap = new HashMap<>();
                initExcel(shows, headers, fields, dictMap, treeMap);

                result.put("count", saveExcel(name, realPath, sql, headers, fields, dictMap, treeMap, null, user, jsonObject));
                result.put("fileRelativeUrl", realPath);
                result.put("respCode", GWConstants.RET_CODE_SUCCESS);
                result.put("respDesc", "导出成功");
            }
        } catch (ServiceException e) {
            logger.error("ServiceException: " + e.getMessage(), e);
            result.put("respCode", GWConstants.RET_CODE_NOT_DATA);
            result.put("respDesc", "导出失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Exception: " + e.getMessage(), e);
            result.put("respCode", GWConstants.RET_CODE_NOT_DATA);
            result.put("respDesc", "导出失败: " + e.getMessage());
        } finally {
            if (GWConstants.RET_CODE_SUCCESS.equals(result.getString("respCode"))) {
                UploadUtil2.upload(entId, busiOrderId, schema, new File(realPath), "export", id, user.getUserAcc());
                FileUtil.deleFile(realPath);
            }
            logger.info(modelCode + " list export time:" + (System.currentTimeMillis() - startMs) + "ms", null);
        }
        return result;
    }


    /**
     * 导出excel文件
     * @param name
     * @param sql
     * @param headers
     * @param fields
     * @param dictMap
     * @param treeMap
     * @param formatMap
     * @param user
     * @param json
     * @return
     * @throws Exception
     */
    private int saveExcel(String name,String realPath, EasySQL sql, List headers, List fields, Map dictMap, Map treeMap, Map formatMap,UserModel user,JSONObject json) throws Exception {
        try  {
            String fileDir = json.getString("fileDir");
            logger.info("导出参数："+json.toJSONString());
            String modelCode = (String) JSONPath.eval(json, "$.expParam.modelCode");
            logger.info("modelCode:" + modelCode);

            File file = new File(fileDir);
            if (file != null && !file.exists()) {
                file.mkdirs();
            }
            ExcelExportDataHandle handle = new ExcelExportDataHandleImpl();
            handle.setEntId(user.getEpCode());
            handle.setBusiOrderId(user.getBusiOrderId());
            handle.setDictMap(dictMap);
            handle.setTreeMap(treeMap);
            handle.setFormatMap(formatMap);
            ExcelExport exportExcel = new ExcelExport().setUser(user).setEasySql(sql)
                    .setFields(fields).setHeaderList(headers).setExcelExportDataHandle(handle).setModelCode(modelCode);
            return exportExcel.save(name, realPath,null);
        } catch (Exception e) {
            logger.error("导出Excel失败：" + e.getMessage(), e);
            throw e; // 重新抛出异常，让调用者知道发生了错误
        }
    }

    /**
     * 获取真实路径
     * @param dir
     * @param name
     * @return
     */
    private String getRealPath(String dir,String name){
        if (StringUtils.isBlank(dir) || StringUtils.isBlank(name)) {
            return "";
        }
        return (dir.endsWith(File.separator) || name.startsWith(File.separator)) ? dir + name : dir + File.separator + name;
    }

    /**
     * 过滤导出安全字段
     * @param user
     * @param datFldDefinitions
     * @return
     */
    private List<XtyDatFldDefinitions> filterField (UserModel user,List<XtyDatFldDefinitions> datFldDefinitions,String modelCode) {
        if (CollectionUtils.isEmpty(datFldDefinitions)) {
            return datFldDefinitions;
        }
        List<JSONObject> fields = CryptorRightUtil.getField("1", user.getSchemaName());
        logger.info("当前用户角色限制范围："+JSONObject.toJSONString(fields));
        return datFldDefinitions.stream().filter(item -> {
            if ("lt-order".equals(modelCode)) {
                return true;
            }
            String fieldName = item.getFieldName();
            if (CollectionUtils.isEmpty(fields)) {
                return true;
            }
            List<String> cryptorField = fields.stream().map(field -> field.getString("CRYPTOR_FIELD")).collect(Collectors.toList());
            if (cryptorField.contains(fieldName)) {
                List<RoleModel> list = user.getRoles();
                if (CollectionUtils.isNotEmpty(list)) {
                    boolean right = CryptorRightUtil.getRight("1", fieldName, user);
                    logger.info("当前字段【"+fieldName+"】是否可以导出"+right+"");
                    return right;
                }
                return false;
            }
            return true;
        }).collect(Collectors.toList());
    }

    /**
     * 初始化excel数据
     * @param shows
     * @param headerList
     * @param fieldList
     * @param dictMap
     * @param treeMap
     */
    public void initExcel(String shows, List headerList, List fieldList, Map dictMap, Map treeMap) {
        List<XtyDatFldDefinitionsPojo> pojos = JSONArray.parseArray(shows, XtyDatFldDefinitionsPojo.class);
        Collections.sort(pojos, Comparator.comparing(XtyDatFldDefinitionsPojo::getSortOrder));
        for (XtyDatFldDefinitionsPojo el : pojos) {
            if (el != null) {
                String fieldName = el.getFieldName();
                headerList.add(el.getFieldText());
                fieldList.add(fieldName);
                String configStr = el.getComponentPropConfig();
                if (StringUtils.isNotBlank(configStr)) {
                    JSONObject obj = JSONObject.parseObject(configStr);
                    if ("select".equals(obj.getString("componentType"))) {
                        if ("1".equals(obj.getString("reqType"))) {
                            dictMap.put(fieldName, obj.getString("dataInf"));
                        } else if ("2".equals(obj.getString("reqType"))) {
                            JSONObject fieldCfg = new JSONObject();
                            fieldCfg.put("code", obj.getString("treeCode"));
                            fieldCfg.put("type", obj.getString("valueType"));
                            treeMap.put(fieldName, fieldCfg);
                        }
                    }

                }
            }
        }
    }

    private int customExport(JSONObject dataModel, List<DatApiParamModel> datApiParamModels, UserModel user,
                            List<XtyDatFldDefinitions> allFldDefinitions, List<XtyDatFldDefinitions> searchFldDefinitions,
                            List<XtyDatFldDefinitions> showFldDefinitions, String resId,
                            String orderIds, String path, String title,Map<String, DatApiParamModel> paramModelMap) throws Exception {
        // 初始化变量
        String dateTime = "";
        // 创建查询对象
        EasyQuery query = getListQuery() ;
        // 获取导出大小
        int size = ExcelUtil.getExpSize(user);
        // 初始化列表
        List<String> headers = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        List<String> cacheList = new ArrayList<>();

        // 获取视图SQL和模型代码
        String viewSql = dataModel.getString("viewSql");
        String modelCode = dataModel.getString("modelCode");
        // 初始化字典和树形结构Map
        Map<String, String> dictMap = new HashMap<>();
        Map<String, JSONObject> treeMap = new HashMap<>();
        // 初始化Excel
        initExcel(JSONObject.toJSONString(showFldDefinitions), headers, fields, dictMap, treeMap);

        // 初始化Excel导出数据处理器
        ExcelExportDataHandle excelExportDataHandle = new ExcelExportDataHandleImpl();
        excelExportDataHandle.setEntId(user.getEpCode());
        excelExportDataHandle.setBusiOrderId(user.getBusiOrderId());
        excelExportDataHandle.setDictMap(dictMap);
        excelExportDataHandle.setTreeMap(treeMap);

        // 获取文件目录并创建目录
        String fileDir = new File(path).getParent();
        Map<String,Integer> sqlMap = new HashMap<>();
        File file = new File(fileDir);
        if (file != null && !file.exists()) {
            file.mkdirs();
        }

        int count = 0;
        int page = 0;
        ExcelWriter excelWriter = null;
        try {
            // 创建ExcelWriter
            excelWriter = EasyExcel.write(path).head(ExcelUtil.formatHeader(headers, title))
                    .registerWriteHandler(CellStyleUtil.getHorizontalCellStyleStrategy()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("sheet1").build();
            List<List<Object>> list = null;
            DatApiParamModel appealTime = paramModelMap.get("APPEAL_TIME");
            JSONArray paramValue = (JSONArray) appealTime.getParamValue();
            String startTime = paramValue.getString(0);
            String endTime = paramValue.getString(1);
            String startDate = new SimpleDateFormat(DateUtil.TIME_FORMAT_YMD)
                    .format(DateUtil.getDate(DateUtil.TIME_FORMAT, startTime));
            String endDate = new SimpleDateFormat(DateUtil.TIME_FORMAT_YMD)
                    .format(DateUtil.getDate(DateUtil.TIME_FORMAT, endTime));
            logger.info("开始执行导出:" + modelCode + ",开始时间：" + startDate + ",结束时间：" + endDate);
            String date = startDate;
            Map<String, Boolean> cryptor = new HashMap<>();
            while (ThreadMgr.isStart()
                    && DateUtil.compareDate(date, endDate,DateUtil.TIME_FORMAT_YMD) <= 0
                    && count < size && Constants.MAGIC_01.equals(Constants.getXtyIsCustomExport())) {
                logger.info("开始执行导出日期" + date + "的数据..." + (DateUtil.compareDate(date, endDate,DateUtil.TIME_FORMAT_YMD) <= 0));
                if (date.equals(startDate)) {
                    paramValue.set(0, startTime);
                } else {
                    paramValue.set(0, date + " 00:00:00");
                }
                date = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD,date,9);
                if (DateUtil.compareDate(date,endDate,DateUtil.TIME_FORMAT_YMD) > 0) {
                    paramValue.set(1, endTime);
                } else {
                    paramValue.set(1, date + " 23:59:59");
                }

                // 循环查询数据直到达到导出大小或停止标志
                while (ThreadMgr.isStart() && count < size
                        && Constants.MAGIC_01.equals(Constants.getXtyIsCustomExport())) {
                    if (StringUtils.isNotBlank(dateTime) && Objects.nonNull(appealTime)
                            && appealTime.getParamValue() instanceof JSONArray) {
                        JSONArray array = (JSONArray) appealTime.getParamValue();
                        if (array.size() > 0) {
                            array.set(1, dateTime);
                        }
                    }
                    List<String> newCacheList = new ArrayList<>();
                    // 生成SQL
                    EasySQL sql = BuildSqlUtils.generateSql(resId, viewSql, datApiParamModels, query.getTypes().name(),
                            searchFldDefinitions, showFldDefinitions, allFldDefinitions, user, !StringUtils.equalsAny(modelCode, Constants.getListNotAuth()));

                    // 添加订单ID条件
                    if (StringUtils.isNotBlank(orderIds)) {
                        sql.appendIn(JSONArray.parseArray(orderIds, String.class).toArray(new String[]{}), " and ID ");
                    }
                    // 添加分页限制
                    sql.append(" limit  " + Math.min((size - count), 1000));
                    String fullSql = sql.getFullSql();
                    Integer interval = sqlMap.get(fullSql);
                    if (interval != null && interval > 10) {
                        logger.info("SQL执行次数过多，跳过...");
                        break;
                    }
                    sqlMap.put(fullSql, interval == null ? 1 : interval + 1);
                    long start = System.currentTimeMillis();
                    logger.info("开始执行第【" + page + "】页导出SQL：" + sql.getFullSq());
                    // 执行查询
                    List<JSONObject> data = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                    logger.info("执行SQL耗时：" + (System.currentTimeMillis() - start) + "ms");
                    if (CollectionUtils.isEmpty(data) || data.size() == 0 ) {
                        logger.info("SQL导出数据为空，跳过...");
                        break;
                    }
                    logger.info("SQL导出数据总数：" + data.size());
                    // 过滤已缓存的数据
                    List<String> finalCacheList = cacheList;
                    List<JSONObject> filterData = data.stream()
                            .filter(item -> {
                                newCacheList.add(item.getString("OID"));
                                return !finalCacheList.contains(item.getString("OID"));
                            })
                            .collect(Collectors.toList());
                    cacheList.clear();
                    cacheList = newCacheList;
                    logger.info("SQL导出数据总数：" + filterData.size());
                    if (CollectionUtils.isEmpty(filterData)) {
                        logger.info("SQL过滤导出数据为空，跳过...");
                        break;
                    }
                    JSONObject object = filterData.get(filterData.size() - 1);
                    // 更新日期时间
                    dateTime = object.getString("APPEAL_TIME");
                    // 转换数据格式并写入Excel
                    list = filterData.stream()
                            .map(item -> {
                                List<Object> values = new ArrayList<>();
                                for (String key : fields) {
                                    String value = String.valueOf(item.get(key));
                                    if ((Objects.nonNull(cryptor.get(key)) && cryptor.get(key)) || filterField(user, key)) {
                                        cryptor.put(key, true);
                                        value = PhoneCryptor.getInstance().decrypt(String.valueOf(value));
                                    }
                                    if (key.endsWith("SMS_TYPE")) {
                                        value = getTemplateName(value,user.getSchemaName());
                                    }
                                    values.add(excelExportDataHandle.handle(key, value));
                                }

                                return values;
                            })
                            .collect(Collectors.toList());
                    excelWriter.write(list, writeSheet);
                    page++;
                    count += list.size();
                    list.clear();
                    Thread.sleep(5);
                }
                dateTime = "";
                date = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD,date, 1);
            }
            logger.info("导出数据总数：" + count);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            // 关闭ExcelWriter
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        return count;
    }


    public  int getIndexOfValue(List<String> list, String valueToFind) {
        for (int i = 0; i < list.size(); i++) {
            if (list.get(i).equals(valueToFind)) {
                return i; // 找到值，返回索引
            }
        }
        return -1; // 未找到值，返回-1
    }

    private boolean filterField (UserModel user, String fieldName) {
        List<JSONObject> fields = CryptorRightUtil.getField("2", user.getSchemaName());
        if (CollectionUtils.isEmpty(fields)) {
            return false;
        }
        List<String> cryptorField = fields.stream().map(field -> field.getString("CRYPTOR_FIELD")).collect(Collectors.toList());
        if (StringUtils.equalsAny(fieldName,cryptorField.toArray(new String[]{}))) {
            //logger.info("当前字段【"+fieldName+"】在限制范围内{"+exportField+"}");
            List<RoleModel> list = user.getRoles();
            if (CollectionUtils.isNotEmpty(list)) {
                return CryptorRightUtil.getRight("2", fieldName, user);
            }
            //logger.info("当前用户角色不在允许导出范围内{"+exportRole+"}");
        }
        return false;
    }

    public EasyQuery getListQuery() {
        // 获取随机整数
        Random rand = new Random();
        // 生成一个范围在1到100之间的随机整数（包括1和100）
        int randomInteger = rand.nextInt(100) + 1;
        EasyQuery easyQuery = null;
        if (randomInteger % 2 == 0) {
            easyQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.LIST_READ_NAME1);
        } else {
            easyQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.LIST_READ_NAME2);
        }
        easyQuery.setLogger(CommonLogger.getLogger("export"));
        return easyQuery;
    }

    private String getTemplateName (String id,String schema) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(id)) {
            return "";
        }
        String key = "MAPPER_XTY_SMS_TEMPLATE_" + id;
        String name = CacheUtil.get(key);
        if (org.apache.commons.lang3.StringUtils.isBlank(name)) {
            try {
                EasySQL sql = new EasySQL();
                sql.append(" select TEMPLATE_NAME from " + schema + ".XTY_PROVINCE_SMS_TEMPLATE ");
                sql.append(id," where ID = ? ",false);
                name = QueryFactory.getReadQuery().queryForString(sql.getSQL(), sql.getParams());
                CacheUtil.put(key,name);
            } catch (Exception e) {
                JDBCLogger.getLogger().error("DecryptMapper.getTemplateName() exception , cause:" + e.getMessage());
            }
        }
        return org.apache.commons.lang3.StringUtils.isBlank(name) ? id : name;
    }
}
