package com.yunqu.tariff.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;

/**
 * ZIP文件验证工具类
 * 提供ZIP文件完整性检查、编码检测等功能
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
public class ZipFileValidator {

    private static final Logger logger = LoggerFactory.getLogger(ZipFileValidator.class);

    /**
     * 验证ZIP文件的完整性和格式
     *
     * @param zipFilePath ZIP文件路径
     * @return 验证结果
     */
    public static ValidationResult validateZipFile(String zipFilePath) {
        ValidationResult result = new ValidationResult();
        
        if (zipFilePath == null || zipFilePath.trim().isEmpty()) {
            result.setValid(false);
            result.setErrorMessage("ZIP文件路径不能为空");
            return result;
        }

        File zipFile = new File(zipFilePath);
        if (!zipFile.exists()) {
            result.setValid(false);
            result.setErrorMessage("ZIP文件不存在: " + zipFilePath);
            return result;
        }

        if (!zipFile.isFile()) {
            result.setValid(false);
            result.setErrorMessage("指定路径不是文件: " + zipFilePath);
            return result;
        }

        if (zipFile.length() == 0) {
            result.setValid(false);
            result.setErrorMessage("ZIP文件为空");
            return result;
        }

        // 检查文件头是否为ZIP格式
        if (!isValidZipFileHeader(zipFile)) {
            result.setValid(false);
            result.setErrorMessage("文件不是有效的ZIP格式");
            return result;
        }

        // 尝试打开ZIP文件进行完整性检查
        try {
            validateZipIntegrity(zipFile, result);
        } catch (Exception e) {
            result.setValid(false);
            result.setErrorMessage("ZIP文件完整性检查失败: " + e.getMessage());
            logger.error("ZIP文件完整性检查失败: " + zipFilePath, e);
        }

        return result;
    }

    /**
     * 检查文件头是否为ZIP格式
     */
    private static boolean isValidZipFileHeader(File file) {
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] header = new byte[4];
            int bytesRead = fis.read(header);
            
            if (bytesRead < 4) {
                return false;
            }
            
            // ZIP文件的魔数: PK (0x504B)
            return (header[0] == 0x50 && header[1] == 0x4B) &&
                   (header[2] == 0x03 && header[3] == 0x04 || // 本地文件头
                    header[2] == 0x05 && header[3] == 0x06 || // 中央目录结束记录
                    header[2] == 0x07 && header[3] == 0x08);  // 跨度标记
        } catch (IOException e) {
            logger.error("检查ZIP文件头失败", e);
            return false;
        }
    }

    /**
     * 验证ZIP文件完整性
     */
    private static void validateZipIntegrity(File zipFile, ValidationResult result) {
        // 首先尝试使用ZipFile类（推荐方式）
        try (ZipFile zf = new ZipFile(zipFile, StandardCharsets.UTF_8)) {
            int entryCount = 0;
            java.util.Enumeration<? extends ZipEntry> entries = zf.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                entryCount++;
                
                // 验证每个条目是否可以正常读取
                try (InputStream is = zf.getInputStream(entry)) {
                    // 读取少量数据以验证条目完整性
                    byte[] buffer = new byte[1024];
                    while (is.read(buffer) != -1) {
                        // 继续读取直到结束
                    }
                }
            }
            
            result.setValid(true);
            result.setEntryCount(entryCount);
            result.setSuggestedCharset(StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            // 如果UTF-8失败，尝试其他编码
            logger.warn("使用UTF-8编码验证ZIP文件失败，尝试其他编码: " + e.getMessage());
            tryAlternativeEncodings(zipFile, result);
        }
    }

    /**
     * 尝试其他编码方式
     */
    private static void tryAlternativeEncodings(File zipFile, ValidationResult result) {
        Charset[] charsets = {
            Charset.forName("GBK"),
            Charset.forName("GB2312"),
            StandardCharsets.ISO_8859_1,
            Charset.defaultCharset()
        };

        for (Charset charset : charsets) {
            try (ZipFile zf = new ZipFile(zipFile, charset)) {
                int entryCount = 0;
                java.util.Enumeration<? extends ZipEntry> entries = zf.entries();
                while (entries.hasMoreElements()) {
                    ZipEntry entry = entries.nextElement();
                    entryCount++;
                    
                    // 简单验证条目可读性
                    try (InputStream is = zf.getInputStream(entry)) {
                        is.read(new byte[1024]);
                    }
                }
                
                result.setValid(true);
                result.setEntryCount(entryCount);
                result.setSuggestedCharset(charset);
                logger.info("ZIP文件使用编码 {} 验证成功", charset.name());
                return;
                
            } catch (Exception e) {
                logger.debug("使用编码 {} 验证ZIP文件失败: {}", charset.name(), e.getMessage());
            }
        }

        // 所有编码都失败，最后尝试使用ZipInputStream
        tryZipInputStream(zipFile, result);
    }

    /**
     * 使用ZipInputStream进行最后尝试
     */
    private static void tryZipInputStream(File zipFile, ValidationResult result) {
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFile), StandardCharsets.UTF_8)) {
            ZipEntry entry;
            int entryCount = 0;
            
            while ((entry = zis.getNextEntry()) != null) {
                entryCount++;
                // 读取少量数据验证
                byte[] buffer = new byte[1024];
                while (zis.read(buffer) != -1) {
                    // 继续读取
                }
                zis.closeEntry();
            }
            
            result.setValid(true);
            result.setEntryCount(entryCount);
            result.setSuggestedCharset(StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            result.setValid(false);
            result.setErrorMessage("ZIP文件损坏或格式不正确: " + e.getMessage());
            logger.error("使用ZipInputStream验证ZIP文件失败", e);
        }
    }

    /**
     * 检测ZIP文件的最佳编码
     */
    public static Charset detectBestCharset(String zipFilePath) {
        ValidationResult result = validateZipFile(zipFilePath);
        return result.isValid() ? result.getSuggestedCharset() : StandardCharsets.UTF_8;
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private boolean valid;
        private String errorMessage;
        private int entryCount;
        private Charset suggestedCharset;

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public int getEntryCount() {
            return entryCount;
        }

        public void setEntryCount(int entryCount) {
            this.entryCount = entryCount;
        }

        public Charset getSuggestedCharset() {
            return suggestedCharset;
        }

        public void setSuggestedCharset(Charset suggestedCharset) {
            this.suggestedCharset = suggestedCharset;
        }

        @Override
        public String toString() {
            return "ValidationResult{" +
                    "valid=" + valid +
                    ", errorMessage='" + errorMessage + '\'' +
                    ", entryCount=" + entryCount +
                    ", suggestedCharset=" + suggestedCharset +
                    '}';
        }
    }
}