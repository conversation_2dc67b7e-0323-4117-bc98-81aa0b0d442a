package com.yunqu.tariff.task;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.enums.AuditTaskStatusEnum;
import com.yunqu.tariff.enums.ProcessStatusEnum;
import com.yunqu.tariff.service.TariffAuditTaskService;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.slf4j.Logger;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 稽核任务远程结果扫描定时任务
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
public class TariffAuditResultScanTask {

    private static final Logger logger = CommonLogger.getLogger("audit-result-scan-task");

    // 线程池，用于并发处理结果文件
    private static final ExecutorService executor = Executors.newFixedThreadPool(3);

    // 每次扫描的最大任务数量
    private static final int MAX_SCAN_COUNT = 10;

    public void run() {
        logger.debug("开始扫描远程结果文件");

        try {
            // 查询SFTP上传完成但远程处理未完成的任务
            List<JSONObject> processingTasks = getProcessingTasks();

            if (processingTasks.isEmpty()) {
                logger.debug("没有需要扫描结果的任务");
                return;
            }

            logger.info("发现 {} 个需要扫描结果的任务", processingTasks.size());

            // 提交任务到线程池处理
            for (JSONObject task : processingTasks) {
                executor.submit(new ResultProcessor(task));
            }

        } catch (Exception e) {
            logger.error("扫描远程结果文件失败", e);
        }
    }

    /**
     * 查询需要扫描结果的任务
     * 查询条件：本地处理完成 AND SFTP上传完成 AND 远程处理中
     */
    private List<JSONObject> getProcessingTasks() {
        try {
            EasySQL sql = new EasySQL();
            sql.append("SELECT * FROM " + Constants.getBusiTable(Constants.AUDIT_TASK_TABLE) + " where 1=1");
            sql.append(ProcessStatusEnum.COMPLETED.getCode(), " AND SFTP_UPLOAD_STATUS = ?");
            sql.append(ProcessStatusEnum.PROCESSING.getCode(), " AND REMOTE_PROCESS_STATUS = ?");
            sql.append(" ORDER BY CREATE_TIME ASC");
            sql.append(MAX_SCAN_COUNT, " LIMIT ?");

            return QueryFactory.getTariffQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

        } catch (Exception e) {
            logger.error("查询处理中任务失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 结果处理器
     */
    private static class ResultProcessor implements Runnable {

        private final JSONObject task;

        public ResultProcessor(JSONObject task) {
            this.task = task;
        }

        @Override
        public void run() {
            String taskId = task.getString("ID");
            String taskName = task.getString("TASK_NAME");

            logger.info("开始扫描任务结果: {} - {}", taskId, taskName);

            try {
                JSONObject task = getTask(taskId);
                String remotePath = task.getString("SFTP_REMOTE_PATH");
                //String ordersFileName = task.getString("ORDERS_FILE_NAME");
                String zipFilePath = task.getString("ZIP_FILE_PATH");
                String zipFileName = new File(zipFilePath).getName();
                String resultFilePath = checkOrdersFileAuditResult(taskId, zipFileName, remotePath);
                if (StringUtils.isBlank(resultFilePath)) {
                    return;
                }
                // 处理解压后的文件
                processExtractedFiles(taskId, resultFilePath);

                // 更新任务状态为远程处理完成
                TariffAuditTaskService.updateProcessStatus(taskId, "remote", ProcessStatusEnum.COMPLETED);
                TariffAuditTaskService.updateTaskStatus(taskId, AuditTaskStatusEnum.REMOTE_COMPLETED, null);

                logger.info("任务远程结果处理完成: {} - {}", taskId, taskName);

            } catch (Exception e) {
                logger.error("处理任务结果失败: {} - {}", taskId, taskName, e);

                // 更新任务状态为失败
                updateTaskStatus(taskId, AuditTaskStatusEnum.FAILED, "远程结果处理异常: " + e.getMessage());

            }
        }


        private String checkOrdersFileAuditResult(String taskId, String ordersFileName, String remotePath) {
            String url = Constants.XTY_PUBLIC_HOST + "/cx-mix-public/tariff/audit/result/check";
            JSONObject params = new JSONObject();
            params.put("taskId", taskId);
            params.put("ordersFileName", ordersFileName);
            params.put("sftpRemotePath", remotePath);
            params.put("callbackUrl", Constants.LOCAL_SERVER_HOST);
            logger.info("开始检查结果文件: {} - {}", taskId, params);
            String body = HttpUtil.createPost(url)
                    .body(params.toString())
                    .execute()
                    .body();
            logger.info("检查结果文件完成: {} - {}", taskId, body);
            if (StringUtils.isBlank(body)) return null;
            JSONObject resultJson = JSONObject.parseObject(body);
            int state = resultJson.getIntValue("state");
            if (state == 1) {
                return resultJson.getString("data");
            }
            return null;
        }


        private JSONObject getTask(String taskId) {
            try {
                EasySQL sql = new EasySQL();
                sql.append("SELECT * FROM " + Constants.getBusiTable(Constants.AUDIT_TASK_TABLE));
                sql.append(taskId, " WHERE ID = ?");

                return QueryFactory.getTariffQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

            } catch (Exception e) {
                logger.error("查询任务失败: taskId={}", taskId, e);
            }
            return null;
        }


        /**
         * 处理解压后的文件
         */
        private void processExtractedFiles(String taskId, String filePath) {
            logger.info("任务 {} 解压得到 {} 个文件", taskId, filePath);
            EasyRecord record = new EasyRecord(Constants.AUDIT_TASK_TABLE, "ID").setPrimaryValues(taskId);
            record.set("SFTP_REMOTE_FIlE_PATH", filePath);
            record.set("SFTP_REMOTE_FIlE_NAME", new File(filePath).getName());
            record.set("REMOTE_PROCESS_STATUS", 2);
            try {
                QueryFactory.getTariffQuery().update(record);
                logger.debug("更新文件路径成功");
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }

        /**
         * 更新数据库中的文件路径
         */
        private void updateFilePathInDB(String taskId, String columnName, String filePath) {
            try {
                String sql = "UPDATE " + Constants.getBusiTable(Constants.AUDIT_TASK_TABLE) +
                        " SET " + columnName + " = ?, UPDATE_TIME = NOW() WHERE ID = ?";

                QueryFactory.getTariffQuery().executeUpdate(sql, new Object[]{filePath, taskId});
                logger.debug("更新文件路径成功: {} = {}", columnName, filePath);

            } catch (Exception e) {
                logger.error("更新文件路径失败: taskId={}, column={}, path={}", taskId, columnName, filePath, e);
            }
        }

        /**
         * 更新任务状态
         */
        private void updateTaskStatus(String taskId, AuditTaskStatusEnum status, String errorMessage) {
            try {
                TariffAuditTaskService.updateTaskStatus(taskId, status, errorMessage);
            } catch (Exception e) {
                logger.error("更新任务状态失败: taskId={}, status={}", taskId, status, e);
            }
        }
    }

    /**
     * 关闭线程池
     */
    public static void shutdown() {
        logger.info("正在关闭远程结果扫描线程池");

        executor.shutdown();

        try {
            // 等待30秒让正在执行的任务完成
            if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                logger.warn("线程池未能在30秒内正常关闭，强制关闭");
                executor.shutdownNow();

                // 再等待10秒
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    logger.error("线程池强制关闭失败");
                }
            }
        } catch (InterruptedException e) {
            logger.error("等待线程池关闭时被中断", e);
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        logger.info("远程结果扫描线程池已关闭");
    }

    /**
     * 获取线程池状态信息
     */
    public static JSONObject getThreadPoolStatus() {
        JSONObject status = new JSONObject();

        if (executor instanceof java.util.concurrent.ThreadPoolExecutor) {
            java.util.concurrent.ThreadPoolExecutor tpe = (java.util.concurrent.ThreadPoolExecutor) executor;

            status.put("corePoolSize", tpe.getCorePoolSize());
            status.put("maximumPoolSize", tpe.getMaximumPoolSize());
            status.put("activeCount", tpe.getActiveCount());
            status.put("taskCount", tpe.getTaskCount());
            status.put("completedTaskCount", tpe.getCompletedTaskCount());
            status.put("queueSize", tpe.getQueue().size());
            status.put("isShutdown", tpe.isShutdown());
            status.put("isTerminated", tpe.isTerminated());
        }

        return status;
    }
}
