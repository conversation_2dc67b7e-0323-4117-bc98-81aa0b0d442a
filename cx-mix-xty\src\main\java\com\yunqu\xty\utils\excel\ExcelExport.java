package com.yunqu.xty.utils.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.RoleModel;
import com.yq.busi.common.model.UserModel;
import com.yunqu.xty.base.CommonLogger;
import com.yunqu.xty.base.Constants;
import com.yunqu.xty.base.QueryFactory;
import com.yunqu.xty.base.mapper.ExportMapper;
import com.yunqu.xty.thread.ThreadMgr;
import com.yunqu.xty.utils.CryptorRightUtil;
import com.yunqu.xty.utils.ExcelUtil;
import com.yunqu.xty.utils.PhoneCryptor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import java.io.OutputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

public class ExcelExport {

	private UserModel user;

	private EasySQL easySql;

	private String sql;

	private Object[] params;

	private List<String> fields;

	private String title;

	private List<String> headerList;

	private ExcelExportDataHandle excelExportDataHandle;

	private static int BATCH_SIZE = 1000;

	private String modelCode;

	private Logger logger = CommonLogger.getLogger("export");

	public void export(String title, OutputStream os, WriteHandler handler) throws SQLException {
		this.title = title;
		export(os, handler);
	}

	public int save(String title, String path,WriteHandler handler) {
		this.title = title;
		return save(path,handler);
	}

	private int save(String path,WriteHandler handler) {
		int size = ExcelUtil.getExpSize(user);
		EasyQuery query = getListQuery();
		query.setTimeout(60);
		int count = 0;
		int page = 0;
		ExcelWriter excelWriter = null;
		try {
			excelWriter = EasyExcel.write(path).head(ExcelUtil.formatHeader(headerList, title))
					.registerWriteHandler(handler)
					.registerWriteHandler(CellStyleUtil.getHorizontalCellStyleStrategy()).build();
			WriteSheet writeSheet = EasyExcel.writerSheet("sheet1").build();
			List<List<Object>> list = null;
			while (ThreadMgr.isStart() && ( count < size || "lt-order".equals(modelCode)))  {

				list = this.getData(query, page,size,count);
				if (CollectionUtils.isEmpty(list)) {
					break;
				}
				excelWriter.write(list, writeSheet);
				page++;
				count += list.size();
				list.clear();
			}
			logger.info("导出数据总数：" + count);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {
			if (excelWriter != null) {
				excelWriter.finish();
			}
		}
		return count;
	}

	private List<List<Object>> getData(EasyQuery query, int page,int size,int count) throws SQLException {
		EasySQL pageSql = new EasySQL(this.easySql.getFullSq());
		pageSql.append(" limit " + page * BATCH_SIZE + "," + ("lt-order".equals(modelCode) ? BATCH_SIZE : size - count));
		long start = System.currentTimeMillis();
		logger.info("开始执行第【"+page+"】页导出SQL："+pageSql.getFullSq());
		List<JSONObject> list = query.queryForList(pageSql.getSQL(), pageSql.getParams(), new ExportMapper(this.excelExportDataHandle, this.user,modelCode));
		logger.info("执行SQL耗时：" + (System.currentTimeMillis() - start) + "ms");
		return list.stream().map(item -> {
			List<Object> values = new ArrayList<>();
			for (String key : fields) {
				String value = String.valueOf(item.get(key));
				values.add(excelExportDataHandle.handle(key, value));
			}
			return values;
		}).collect(Collectors.toList());
	}

	private void export(OutputStream os, WriteHandler handler) throws SQLException {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;

		ExcelWriter excelWriter = EasyExcel.write(os).head(ExcelUtil.formatHeader(headerList, title))
				.registerWriteHandler(handler)
				.registerWriteHandler(CellStyleUtil.getHorizontalCellStyleStrategy()).build();
		WriteSheet writeSheet = EasyExcel.writerSheet("sheet1").build();
		try {
			int size = ExcelUtil.getExpSize(user);
			EasyQuery query = QueryFactory.getReadQuery();
			conn = query.getConnection();
			ps = conn.prepareStatement(this.sql);
			if(size != 0){
				ps.setMaxRows(size);
			}
			for (int i = 0; i < params.length; i++) {
				Object param = params[i];
				if (param instanceof String) {
					ps.setString(i + 1, (String) param);
				} else if (param instanceof Integer) {
					ps.setInt(i + 1, (Integer) param);
				}
			}
			rs = ps.executeQuery();
			List<List<Object>> list = new ArrayList<List<Object>>();
			while (rs.next()) {
				List<Object> data = new ArrayList<Object>();
				for (int i = 0; i < headerList.size(); i++) {
					String fieldName = null;
					if (fields != null && fields.size() > i) {
						fieldName = fields.get(i);
					}
					String value = "";
					value = StringUtils.isNotBlank(fieldName) ? rs.getString(fieldName) : rs.getString(i + 1);
					if (excelExportDataHandle != null) {
						value = excelExportDataHandle.handle(fieldName,value);
					}
					if (filterField(user, fieldName)) {
						value = PhoneCryptor.getInstance().decrypt(value);
					}
					if (StringUtils.isNotBlank(value)&&value.matches("[0-9]{1,6}+")) {
						data.add(Long.valueOf(value));
					} else if (StringUtils.isNotBlank(value)&&value.matches("^([0-9]{1,6}[.][0-9]*)$")){
						data.add(Double.valueOf(value));
					} else {
						data.add(StringUtils.isNotBlank(value)?value:"");
					}

				}
				list.add(data);
				if (list.size() % 1000 == 0) {
					excelWriter.write(list, writeSheet);
					list.clear();
				}
			}
			excelWriter.write(list, writeSheet);
		} catch (SQLException e) {
			logger.error(e.getMessage(), e);
		} finally {
			closeResourceWithLogging(rs, "ResultSet");
			closeResourceWithLogging(ps, "PreparedStatement");
			closeResourceWithLogging(conn, "Connection");

			if (excelWriter != null) {
				excelWriter.finish();
			}
		}
	}


	// 添加一个辅助方法来关闭资源并记录异常
	private void closeResourceWithLogging(AutoCloseable resource, String resourceType) {
		if (resource != null) {
			try {
				resource.close();
			} catch (Exception e) {
				logger.error("Error closing " + resourceType + ": " + e.getMessage(), e);
			}
		}
	}

	private boolean filterField (UserModel user, String fieldName) {
		List<JSONObject> fields = CryptorRightUtil.getField("2", user.getSchemaName());
		if (CollectionUtils.isEmpty(fields)) {
			return false;
		}
		List<String> cryptorField = fields.stream().map(field -> field.getString("CRYPTOR_FIELD")).collect(Collectors.toList());
		if (StringUtils.equalsAny(fieldName,cryptorField.toArray(new String[]{}))) {
			//logger.info("当前字段【"+fieldName+"】在限制范围内{"+exportField+"}");
			List<RoleModel> list = user.getRoles();
			if (CollectionUtils.isNotEmpty(list)) {
				return CryptorRightUtil.getRight("2", fieldName, user);
			}
			//logger.info("当前用户角色不在允许导出范围内{"+exportRole+"}");
		}
		return false;
	}

	public EasySQL getEasySql() {
		return easySql;
	}

	public ExcelExport setEasySql(EasySQL easySql) {
		this.easySql = easySql;
		this.sql = easySql.getSQL();
		this.params = easySql.getParams();
		return this;
	}

	public String getSql() {
		return sql;
	}

	public ExcelExport setSql(String sql) {
		this.sql = sql;
		return this;
	}

	public Object[] getParams() {
		return params;
	}

	public ExcelExport setParams(Object[] params) {
		this.params = params;
		return this;
	}

	public String getTitle() {
		return title;
	}

	public ExcelExport setTitle(String title) {
		this.title = title;
		return this;
	}

	public List<String> getHeaderList() {
		return headerList;
	}

	public ExcelExport setHeaderList(List<String> headerList) {
		this.headerList = headerList;
		return this;
	}

	public List<String> getFields() {
		return fields;
	}

	public ExcelExport setFields(List<String> fields) {
		this.fields = fields;
		return this;
	}

	public ExcelExport setUser(UserModel user) {
		this.user = user;
		return this;
	}

	public ExcelExportDataHandle getExcelExportDataHandle() {
		return excelExportDataHandle;
	}

	public ExcelExport setExcelExportDataHandle(ExcelExportDataHandle excelExportDataHandle) {
		this.excelExportDataHandle = excelExportDataHandle;
		return this;
	}

	public String getModelCode() {
		return modelCode;
	}

	public ExcelExport setModelCode(String modelCode) {
		this.modelCode = modelCode;
		return this;
	}

	public EasyQuery getListQuery() {
		// 获取随机整数
		Random rand = new Random();
		// 生成一个范围在1到100之间的随机整数（包括1和100）
		int randomInteger = rand.nextInt(100) + 1;
		EasyQuery easyQuery = null;
		 if (randomInteger % 2 == 0) {
			easyQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.LIST_READ_NAME1);
		} else {
			easyQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.LIST_READ_NAME2);
		}
		easyQuery.setLogger(CommonLogger.getLogger("export"));
		return easyQuery;
	}
}
