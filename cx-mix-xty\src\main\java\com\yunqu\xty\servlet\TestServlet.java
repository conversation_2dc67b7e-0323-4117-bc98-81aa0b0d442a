package com.yunqu.xty.servlet;

import com.alibaba.fastjson.JSONObject;
import com.aspose.slides.internal.m.id;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.PhoneCryptor;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.xty.base.AppBaseServlet;
import com.yunqu.xty.base.CommonLogger;
import com.yunqu.xty.base.Constants;
import com.yunqu.xty.base.QueryFactory;
import com.yunqu.xty.excuotr.EventDispatcher;
import com.yunqu.xty.service.BusiOrderService;
import com.yunqu.xty.service.ImportDataService;
import com.yunqu.xty.service.OrderExtendTodoService;
import com.yunqu.xty.service.OrderProvinceCfgService;
import com.yunqu.xty.service.OrderService;
import com.yunqu.xty.utils.ClassTools;
import com.yunqu.xty.utils.DutyUtil;
import com.yunqu.xty.utils.OptionLogUtil;
import com.yunqu.xty.utils.OrderNodeUtil;
import com.yunqu.xty.utils.OrderUtils;
import com.yunqu.xty.utils.WorkTimeUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.RandomKit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.annotation.WebServlet;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@WebServlet("/servlet/test/*")
public class TestServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	private Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("test").getName());


	public JSONObject actionForExtractJudge() {
		try {
			logger.info("开始执行企业抽样");
			String entId = Constants.getEntId();
			String busiOrderId = SchemaService.getCcBusiOrderIdByEntId(entId);
			String schema = SchemaService.findSchemaByEntId(entId);
			String processId = Constants.DUTY_FLOW_KEY;
			String nodeId = Constants.DUTY_PROCESS_NODE_JUDGEMENT_CHECK;
			String fixStatus = Constants.AFFIRM_STATUS_21;
			String currentDate = DateUtil.getCurrentDateStr("yyyy-MM-dd");
			String dealDateStr = "2025-03-15";

			List<String> entGroups = Arrays.asList(
					Constants.GROUP_TYPE_GE_TELECOM,
					Constants.GROUP_TYPE_GE_BROAD,
					Constants.GROUP_TYPE_GE_MOBILE,
					Constants.GROUP_TYPE_GE_UNICOM
			); // 企业类型

			for (String entGroup : entGroups) {
				logger.info("企业类型[{}]开始抽样", entGroup);
				JSONObject entConfig = DutyUtil.getEntConfig(entGroup, schema);
				if (entConfig == null) {
					throw new RuntimeException("企业类型配置不存在");
				}
				//从entConfig中获取省份配置信息$.CONFIG_JSON.PROVINCE_CONFIG
				JSONObject object = entConfig.getJSONObject("CONFIG_JSON");
				String provinceConfigStr = object.getString("PROVINCE_CONFIG");
				logger.info("企业类型[{}]配置信息[{}]", entGroup, provinceConfigStr);
				if (StringUtils.isNotBlank(provinceConfigStr)) {
					List<JSONObject> list = JSONObject.parseArray(provinceConfigStr, JSONObject.class);
					for (JSONObject provinceConfig : list) {
						String provinceCode = provinceConfig.getString("PROVINCE_CODE");
						JSONObject nodeJson = OrderNodeUtil.getInstance().getNodeInfo(processId, Constants.DUTY_PROCESS_NODE_JUDGEMENT, entId, provinceCode, schema);
						JSONObject orderInfo = new JSONObject() {{
							put("ENT_TYPE", Constants.getEntTypeByGroupType(entGroup));
							put("ENT_DEPT_CODE", provinceConfig.getString("DEPT_CODE"));
						}};
						DutyUtil.getFullNodeInfo(nodeJson,
								orderInfo,schema,Constants.DUTY_PROCESS_NODE_JUDGEMENT);
						if (nodeJson == null) {
							logger.error("省份[{}]配置信息不全，无法分配", provinceCode);
							continue;
						}
						nodeJson.put("PROCESS_ID", processId);
						JSONObject configJson = ClassTools.getClass(OrderProvinceCfgService.class).getProvinceCfg(schema, provinceCode, "0");
						if (configJson == null) {
							logger.info("省份[{}]配置信息不存在", provinceCode);
							continue;
						}

						String timeStyleId = configJson.getString("TIME_STYLE_ID");
						String dealDayType = nodeJson.getString("DEAL_DAY_TYPE");
						String dealDays = nodeJson.getString("DEAL_DAYS");
						float sampleRatio = provinceConfig.getFloatValue("EXTRACTION_RATIO");

						logger.info("省份[{}]配置信息dealTimes[{}] dealTimeType[{}] sampleRatio[{}]", provinceCode, dealDays, dealDayType, sampleRatio);

						String dealDate = calculateDealDate(entId,schema,dealDateStr, dealDays, dealDayType, timeStyleId, currentDate);
						EasySQL sql = createSql(entId, schema, provinceCode, fixStatus, dealDate, processId, nodeId, entGroup);

						logger.info("省份[{}]开始查询数据日期[{}]", provinceCode, dealDate);
						List<JSONObject> orderList = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

						nodeJson = OrderNodeUtil.getInstance().getNodeInfo(processId, nodeId, entId, provinceCode, schema);
						DutyUtil.getFullNodeInfo(nodeJson, orderInfo,schema,nodeId);
						nodeJson.put("PROCESS_ID", processId);
						if (nodeJson == null) {
							logger.error("省份[{}]配置信息不全，无法分配", provinceCode);
							continue;
						}
						if (sampleRatio == 100) {
                        } else {
							JSONObject dateRange = getDateRange(schema, dealDate);
							String startDate = dateRange.getString("START_DATE");
							String endDate = dateRange.getString("END_DATE");
							if (StringUtils.isBlank(startDate)) {
								startDate = dealDateStr;
							}

							JSONObject entCounts = getExtractCount3(schema, startDate, endDate, dealDate, fixStatus, provinceCode, nodeId, entGroup);
							int dutyOrder = entCounts.getIntValue("DUTY_ORDER");
							int extractOrder = entCounts.getIntValue("EXTRACT_ORDER");
							int sampleCount = Math.round(dutyOrder * sampleRatio / 100) - extractOrder;
							sampleCount = Math.min(sampleCount, orderList.size());
							logger.info("初判审核任务抽取：省份[{}]执行日期[{}]抽样日期[{}]抽样集团[{}]定责工单总量[{}]定责已抽取工单量[{}] 抽取比例[{}] 抽取数量[{}]", provinceCode,currentDate, dealDate, entGroup, dutyOrder, extractOrder, sampleRatio, sampleCount);
						}
						// this.updateTaskCancel(schema,Constants.DUTY_PROCESS_NODE_JUDGEMENT,dealDate,provinceCode,entGroup);
					}

				}
			}
		} catch (Exception e) {
			logger.error("定责任务分配异常: " + e.getMessage(), e);

		}
		return null;
	}

	public JSONObject getExtractCount3(String schema, String startDate, String endDate, String currDate,String status, String provinceCode,String nodeId,String groupType) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" select sum(DUTY_ORDER) DUTY_ORDER ,sum(EXTRACT_ORDER) EXTRACT_ORDER from (");
			sql.append(" select count(distinct t1.ID) DUTY_ORDER,sum(case when t1.ASSIGN_RESULT = '01' then 1 else 0 end) EXTRACT_ORDER ");
			sql.append(" from " + schema + ".xty_task_allocation_record t1");
			sql.append(nodeId," right join " + schema + ".c_box_duty_order t2 on t1.DUTY_ORDER_ID = t2.ID and t1.NODE_ID = ? ");
			sql.append(" where 1=1 ");
			sql.append(startDate + " 00:00:00"," and t2.APPEAL_TIME >= ? ");
			sql.append(endDate + " 23:59:59"," and t2.APPEAL_TIME <= ? ");
			sql.append(provinceCode," and t2.PROVINCE_CODE = ? ");
			sql.append(Constants.getEntTypeByGroupType(groupType)," and t2.ENT_TYPE = ? ");
			sql.append(" union all ");
			sql.append(" select count(distinct t1.ID) DUTY_ORDER,0 EXTRACT_ORDER ");
			sql.append(" from " + schema + ".c_box_duty_order t1 ");
			sql.append(nodeId," left join " + schema + ".xty_task_allocation_record t2 on t2.DUTY_ORDER_ID = t1.ID and t2.NODE_ID = ? ");
			sql.append(" where 1=1 ");
			sql.append(" and t2.ID is null ");
			sql.append(provinceCode," and t1.PROVINCE_CODE = ? ");
			sql.append(status," and t1.FIX_STATUS = ? ");
			sql.append(currDate + " 00:00:00", "and t1.APPEAL_TIME >= ?");
			sql.append(currDate + " 23:59:59", "and t1.APPEAL_TIME <= ?");
			if(StringUtils.equals(Constants.AFFIRM_STATUS_3, status)) {
				sql.append("02", "and t1.JUDGEMENT_ADUIT_RESULT = ? ");
			}
			sql.append(Constants.getEntTypeByGroupType(groupType)," and t1.ENT_TYPE = ? ");
			sql.append(") temp ");
			logger.info("获取当前周期的抽取信息：" + sql.getFullSq());
			return QueryFactory.getWriteQuery().queryForRow(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return null;
	}

	private JSONObject getDateRange(String schema, String dealDate) throws SQLException {
		return QueryFactory.getReadQuery().queryForRow(
				"select max(t1.DATE_VALUE) END_DATE, min(t1.DATE_VALUE) START_DATE " +
						"from " + Constants.getStatSchema() + ".XTY_DIM_DATE t1, " + Constants.getStatSchema() + ".XTY_DIM_DATE t2 " +
						"where t1.P_YEAR = t2.P_YEAR and t1.P_MONTH = t2.P_MONTH and t2.DATE_VALUE = ?",
				new Object[]{dealDate},
				new JSONMapperImpl()
		);
	}

	private EasySQL createSql(String entId, String schema, String provinceCode, String fixStatus, String dealDate, String processId, String nodeId,String groupType) {
		EasySQL sql = new EasySQL("select t1.ID,t1.PROVINCE_CODE,t1.CITY_CODE,t1.ENT_TYPE,t1.P_ID,t1.APPEAL_TIME ");
		sql.append("from " + schema + ".C_BOX_DUTY_ORDER t1");
		sql.append("left join " + schema + ".XTY_RU_TASK t2 on t1.ID = t2.M_ID").append(processId, "and FLOW_KEY = ?").append(nodeId, "and NODE_ID = ?");
		sql.append("where 1=1");
		sql.append(entId, "and t1.ENT_ID = ?");
		sql.append(fixStatus, "and t1.FIX_STATUS = ?");
		if(StringUtils.equals(Constants.AFFIRM_STATUS_3, fixStatus)) {
			sql.append("02", "and t1.JUDGEMENT_ADUIT_RESULT = ?");
		}
		if(StringUtils.equals(Constants.AFFIRM_STATUS_21, fixStatus)) {
			sql.append("02", "and t1.JUDGEMENT_RESULT = ?");
		}
		sql.append(provinceCode, "and t1.PROVINCE_CODE = ?");
		sql.append(dealDate + " 00:00:00", "and t1.APPEAL_TIME >= ?");
		sql.append(dealDate + " 23:59:59", "and t1.APPEAL_TIME <= ?");
		sql.append("and t2.ID is null");
		if (StringUtils.isNotBlank(groupType)) {
			sql.append(Constants.getEntTypeByGroupType(groupType), "and t1.ENT_TYPE = ?");
		}
		logger.info("抽取SQL：" + sql.getFullSq());
		return sql;
	}

	private String calculateDealDate(String entId,String schema,String dealDateStr, String dealDays, String dealDayType, String timeStyleId, String currentDate) {
		if (StringUtils.isBlank(dealDateStr)) {
			if (StringUtils.isNotBlank(dealDays)) {
				return "1".equals(dealDayType)
						? DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, currentDate, -Integer.parseInt(dealDays))
						: WorkTimeUtils.getWorkStartDate2(entId, schema, timeStyleId, currentDate, currentDate, Integer.parseInt(dealDays));
			}
		}
		return dealDateStr;
	}


//	public JSONObject actionForTypicalFix() {
//		try {
//			EasySQL sql = new EasySQL();
//			sql.append("select * from (");
//			sql.append("select t1.M_ID,t1.TYPICAL_REASON,t2.NODE_ID,t2.NODE_NAME,t2.HANDLE_NAME,t2.HANDLE_ACC,t2.DEPT_CODE,t2.DEPT_NAME,ROW_NUMBER() over(PARTITION BY t1.M_ID order by t2.CREATE_TIME desc) No");
//			sql.append("from " + this.getDbName() + ".c_box_appeal_order t1 left join " + this.getDbName() + ".xty_ru_task_his t2 ON t1.M_ID = t2.M_ID");
//			sql.append("left join " + this.getDbName() + ".xty_order_typical_case t3 ON t1.M_ID = t3.ORDER_ID");
//			sql.append("where t1.PROVINCE_NAME = '河北'");
////			sql.append("where 1=1");
//			sql.append("and t3.id is null and t1.IS_TYPICAL = '01' and t2.NODE_NAME = '处理结果审核') temp ");
//			sql.append("where No = 1");
//			List<JSONObject> jsonObjectList = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
//
//			if (jsonObjectList == null) {
//				return EasyResult.fail("列表查询为空");
//			}
//
//			List<List<JSONObject>> lists = CollUtil.split(jsonObjectList, 30);
//
//			for (List<JSONObject> list : lists) {
//
//				// 批量新增典型案例记录
//				String insertCaseSql = "insert into " + this.getTableName("xty_order_typical_case") + " (ID, ORDER_ID, MARK_USER_ACC, MARK_USER_NAME, MARK_DEPT_CODE, MARK_DEPT_NAME, MARK_REASON, MARK_NODE_ID, MARK_NODE_NAME, NODE_ID, CREATE_TIME, UPDATE_TIME) values (?,?,?,?,?,?,?,?,?,?,?,?)";
//				List<Object[]> params = new ArrayList<>();
//
//				// 添加可见范围关联记录
//				String insertRelationSql = "insert into " + this.getTableName("xty_order_typical_case_rs") + "(ID, CASE_ID, USER_ACC, CREATE_TIME) values (?,?,?,?)";
//				List<Object[]> relationParams = new ArrayList<>();
//
//				for (JSONObject item : list) {
//					String orderId = item.getString("M_ID");
//					String userName = item.getString("HANDLE_NAME");
//					String userAcct = item.getString("HANDLE_ACC");
//					String deptCode = item.getString("DEPT_CODE");
//					String deptName = item.getString("DEPT_NAME");
//					String markReason = item.getString("TYPICAL_REASON");
//					if (StringUtils.isBlank(markReason)) {
//						markReason = "测试工单";
//					}
//					if (markReason.length() > 200) {
//						markReason = markReason.substring(0, 200);
//					}
//					String markNodeId = item.getString("NODE_ID");
//					String markNodeName = item.getString("NODE_NAME");
//					String nodeId = "01";
//					// 新增典型案例记录参数
//					String caseId = IDGenerator.getDefaultNUMID();
//					String currentDateStr = DateUtil.getCurrentDateStr();
//					Object[] oneParam = {caseId, orderId, userAcct, userName, deptCode, deptName, markReason, markNodeId, markNodeName, nodeId, currentDateStr, currentDateStr};
//					params.add(oneParam);
//
//					List<String> userArrays = Collections.singletonList(userAcct);
//					if (CollUtil.isNotEmpty(userArrays)) {
//						// 添加可见范围关联记录参数
//						userArrays.forEach(userAcc -> {
//							Object[] relationParam = {IDGenerator.getDefaultNUMID(), caseId, userAcc, DateUtil.getCurrentDateStr()};
//							relationParams.add(relationParam);
//						});
//					}
//				}
//
//				this.getQuery().executeBatch(insertCaseSql, params);
//				logger.info("新增典型案例记录sql:" + insertCaseSql + "|参数：" + JSONObject.toJSONString(params));
//				this.getQuery().executeBatch(insertRelationSql, relationParams);
//				logger.info("新增可见范围关联记录sql:" + insertRelationSql + "|参数：" + JSONObject.toJSONString(relationParams));
//			}
//
//		} catch (Exception e) {
//			logger.error(e.getMessage(), e);
//		}
//		return EasyResult.ok();
//	}

	public JSONObject actionForGenerateAffirm () throws SQLException {
		EasySQL sql = new EasySQL();
		sql.append(" select * from " + getTableName("c_box_duty_order t1") );
		sql.append(" where 1=1 " );
		sql.append("2023-12-21 00:00:00"," and APPEAL_TIME >= ? ");
		sql.append("2024-01-20 23:59:59"," and APPEAL_TIME <= ? ");
		sql.append(" and t1.JUDGEMENT_RESULT = '02'");
		sql.append(" and not exists (");
		sql.append(" select 1 from " + getTableName("xty_task_allocation_record") + " t2 where t2.duty_order_id = t1.id and t2.node_id = 'affirm')");
		List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		if (CollectionUtils.isEmpty(list)) {
			return EasyResult.ok();
		}
		list.stream().forEach(item -> {
			EasyRecord record = new EasyRecord(getTableName("xty_task_allocation_record"),"ID");
			record.put("ID", IDGenerator.getDefaultNUMID());
			record.put("PROFESSOR_ID",Constants.DUTY_FLOW_KEY);
			record.put("DUTY_ORDER_ID",item.getString("ID"));
			record.put("NODE_ID","affirm");
			record.put("CREATE_TIME","2024-03-28 23:51:01");
			record.put("ASSIGN_RESULT","02");
			try {
				this.getQuery().save(record);
			} catch (SQLException throwables) {
				throwables.printStackTrace();
			}
		});
		return EasyResult.ok();
	}

	public JSONObject actionForInitMediateDateModel () throws SQLException {
		String aClass = this.getPara("class");
		String code = this.getPara("code");
		EasySQL sql = new EasySQL();
		sql.append(" select * from " + ("cc_lcp_dat_model"));
		sql.append(" where 1=1 " );
		sql.append(aClass," and CLASS_ID = ? ",false);
		sql.append(code," and MODEL_CODE != ? ",false);
		List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		EasySQL field = new EasySQL();
		field.append(" select FIELD_NAME,FIELD_TEXT,COMPONENT_PROP_CONFIG from " + ("cc_lcp_dat_fld_definitions") );
		field.append(" where 1=1 ");
		field.append(code," and REF_CODE = ? ",false);
		List<JSONObject> fields = this.getQuery().queryForList(field.getSQL(), field.getParams(), new JSONMapperImpl());
		Map<String, JSONObject> map = fields.stream().collect(Collectors.toMap(item -> item.getString("FIELD_NAME"), item -> item,(o1,o2) -> o1));
		if (CollectionUtils.isNotEmpty(list)) {
			list.stream().forEach(obj -> {
				try {
					String modelCode = obj.getString("MODEL_CODE");
					CommonLogger.getLogger("model-test").info("更新模型： " + JSONObject.toJSONString(obj));
					EasySQL sql1 = new EasySQL();
					sql1.append(" select * from " + ("cc_lcp_dat_fld_definitions") );
					sql1.append(" where 1=1 ");
					sql1.append(modelCode," and REF_CODE = ? ");
					List<JSONObject> objects = this.getQuery().queryForList(sql1.getSQL(), sql1.getParams(), new JSONMapperImpl());
					objects.stream().forEach(o -> {
						String fieldName = o.getString("FIELD_NAME");
						JSONObject object = map.get(fieldName);
						if (Objects.nonNull(object)) {
							EasyRecord record = new EasyRecord(("cc_lcp_dat_fld_definitions"),"ID");
							record.putAll(o);
							record.put("FIELD_TEXT",object.getString("FIELD_TEXT"));
							JSONObject propConfig = object.getJSONObject("COMPONENT_PROP_CONFIG");
							if (Objects.nonNull(propConfig) && !"01".equals(propConfig.getString("search_require"))) {
								record.put("COMPONENT_PROP_CONFIG",propConfig.toJSONString());
							}
							try {
								logger.info("update field:" + JSONObject.toJSONString(record));
								this.getQuery().update(record);
							} catch (SQLException throwables) {
								throwables.printStackTrace();
							}
						}
					});
				} catch (Exception e) {

				}

			});
		}
		return EasyResult.ok();
	}

	/*public JSONObject actionForRepairMediateData () {
		int i = 0;
		while (true) {
			List<Object[]> list = new ArrayList<>();
			try {
				List<JSONObject> data = this.getData(i);
				if (CollectionUtils.isEmpty(data)) {
					break;
				}
				logger.info("处理条数：" + data.size());
				i = i + data.size();
				data.stream().forEach(item -> {
					logger.info("开始处理1" );
					try {
						String mId = item.getString("M_ID");
						Object[] objects = new Object[]{"","","","",mId};
						List<JSONObject> hisTask = this.getHisTask(mId);
						if (CollectionUtils.isNotEmpty(hisTask)) {
							logger.info("任务条数：" + data.size());
							hisTask.stream().forEach(his-> {
								try {
									String nodeId = his.getString("NODE_ID");
									if (Constants.MEDIATE_DEAL.equals(nodeId)) {
										objects[0] = his.getString("COMPLETE_TIME");
									}
									if (Constants.MEDIATE_REPLY.equals(nodeId)) {
										objects[1] = his.getString("COMPLETE_TIME");
									}
									if (Constants.MEDIATE_JUDGEMENT.equals(nodeId)) {
										objects[2] = his.getString("COMPLETE_TIME");
									}
									if (Constants.MEDIATE_ENTDEAL.equals(nodeId)) {
										objects[3] = his.getString("COMPLETE_TIME");
									}
								} catch (Exception e) {
									logger.error(e.getMessage(),e);
								}
							});
							list.add(objects);
						}
					} catch (Exception e) {
						logger.error(e.getMessage(),e);
					}
				});
				if (CollectionUtils.isNotEmpty(list)) {
					this.batchUpdateMediate(list);
				}
			} catch (Exception e) {
				logger.error(e.getMessage(),e);
				break;
			}
		}
		return EasyResult.ok();
	}
*/
	private void batchUpdateMediate (List<Object[]> data) throws SQLException {
		String sql = "update " + getTableName("c_box_mediate_order ") + " set IS_ACCEPT_TIME=?,REPLY_TIME=?,NOTIFY_MEDIATE_TIME=?,MEDIATE_TIME=? where M_ID =? ";
		this.getQuery().executeBatch(sql,data);
	}

	private List<JSONObject> getData (int start) throws SQLException {
		EasySQL sql = new EasySQL();
		sql.append(" select * from " + getTableName("c_box_mediate_order"));
		sql.append(" order by create_time asc ");
		sql.append(" limit " + start + ",1000 ");
		return this.getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
	}

	private List<JSONObject> getHisTask (String mId) throws SQLException {
		EasySQL sql = new EasySQL();
		sql.append("select NODE_ID ,max(COMPLETE_TIME) COMPLETE_TIME  from  " + getTableName("xty_ru_task_his") +
				" where FLOW_KEY ='xty_tj'  and IS_COMPLETE ='01' " );
		sql.append(mId," and M_ID = ? ");
		sql.append("group by NODE_ID" );
		logger.info("task sql:" + sql.getFullSq());
		return this.getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
	}
	/*
	public JSONObject actionForTest0008() {
		try {
			String entId = this.getEntId();
			String schema = this.getDbName();
			EasySQL sql = new EasySQL("select * from " + this.getTableName("C_BOX_APPEAL_ORDER") + " where CLASS_CODE3 is null");
			int i = 1;
			while(true) {
				List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), i, 100, new JSONMapperImpl());
				if(CommonUtil.listIsNull(list)) {
					break;
				}
				i++;

				for(JSONObject row : list) {
					String serviceName1 = row.getString("SERVICE_NAME1");
					String serviceName2 = row.getString("SERVICE_NAME2");
					String serviceName3 = row.getString("SERVICE_NAME3");

					String codeName1 = row.getString("CLASS_NAME1");
					String codeName2 = row.getString("CLASS_NAME2");
					String codeName3 = row.getString("CLASS_NAME3");

					if(StringUtils.isNotBlank(codeName3)) {
						String id = row.getString("ID");
						String mId = row.getString("M_ID");

						logger.info("开始更新 M_ID[" + mId + "]");
						EasyRecord record = new EasyRecord(this.getTableName("C_BOX_APPEAL_ORDER"), "ID");
						record.put("ID", id);
						record.put("CLASS_CODE1", ClassCodeUtils.getCache(entId, schema, codeName1));
						record.put("CLASS_CODE2", ClassCodeUtils.getCache(entId, schema, codeName1 + codeName2));
						record.put("CLASS_CODE3", ClassCodeUtils.getCache(entId, schema, codeName1 + codeName2 + codeName3));
						record.put("SERVICE_CODE3", BusiCodeUtils.getCache(entId, schema, serviceName1 + serviceName2 + serviceName3));
						this.getQuery().update(record);

						JSONObject recordRow = this.getQuery().queryForRow("select ID,ADVICE_CLASS_CODE1,ADVICE_SERVICE_CODE1 from " + this.getTableName("C_BOX_CLASS_RECORD") + " where M_ID = ? and (ADVICE_CLASS_CODE1 is null or ADVICE_SERVICE_CODE1 is null)", new Object[] {mId}, new JSONMapperImpl());
						if(recordRow == null) {
							continue;
						}
						String recordId = recordRow.getString("ID");

						record = new EasyRecord(this.getTableName("C_BOX_CLASS_RECORD"), "ID");
						record.put("ID", recordId);
						if(StringUtils.isBlank(recordRow.getString("ADVICE_CLASS_CODE1"))) {
							record.put("ADVICE_CLASS_CODE1", row.getString("CLASS_CODE1"));
							record.put("ADVICE_CLASS_CODE2", row.getString("CLASS_CODE2"));
							record.put("ADVICE_CLASS_CODE3", ClassCodeUtils.getCache(entId, schema, codeName1 + codeName2 + codeName3));
							record.put("ADVICE_CLASS_NAME1", codeName1);
							record.put("ADVICE_CLASS_NAME2", codeName2);
							record.put("ADVICE_CLASS_NAME3", codeName3);
						} else if(StringUtils.isBlank(recordRow.getString("ADVICE_SERVICE_CODE1"))) {
							record.put("ADVICE_SERVICE_CODE1", row.getString("SERVICE_CODE1"));
							record.put("ADVICE_SERVICE_CODE2", row.getString("SERVICE_CODE2"));
							record.put("ADVICE_SERVICE_CODE3", BusiCodeUtils.getCache(entId, schema, serviceName1 + serviceName2 + serviceName3));
							record.put("ADVICE_SERVICE_NAME1", serviceName1);
							record.put("ADVICE_SERVICE_NAME2", serviceName2);
							record.put("ADVICE_SERVICE_NAME3", serviceName3);
						}
						this.getQuery().update(record);
					}
				}
			}

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public void actionForClassTest() {
		String code1 = ClassCodeUtils.getCache(this.getEntId(), this.getDbName(), "服务服务退停业务被关停");
		logger.info("code1:" + code1);
		String code2 = ClassCodeUtils.getCache(this.getEntId(), this.getDbName(), "安全信息安全电信诈骗");
		logger.info("code2:" + code2);
		String code3 = ClassCodeUtils.getCache(this.getEntId(), this.getDbName(), "服务服务退停业务停机或销号");
		logger.info("code1:" + code3);
	}

	public void actionForDelCache () {
		String s = "u0BVx1u07+iLxGdDoyC3dA==";
		while (ThreadMgr.isStart()) {
			Object o = CacheUtil.get(s);
			if (Objects.nonNull(o)) {
				CacheUtil.delete(s);
				logger.info("删除缓存：" + s );
				CommonUtil.sleep(2);
			} else {
				logger.info("缓存不存在，休息五秒：" + s );
				CommonUtil.sleep(5);
			}
		}
	}

	public void actionForPutCache () {
		CacheUtil.put(Constants.CACHE_KEY_DISTINCT_ORDER + "17132555216063029093781","test");
		CacheUtil.put(Constants.CACHE_KEY_DISTINCT_ORDER + "17127279206202272544375","test");
		CacheUtil.put(Constants.CACHE_KEY_DISTINCT_ORDER + "17127098186951112464577","test");
		CacheUtil.put(Constants.CACHE_KEY_DISTINCT_ORDER + "17132521506155637923849","test");
		CacheUtil.put(Constants.CACHE_KEY_DISTINCT_ORDER + "17128036388657332811142","test");
		CacheUtil.put(Constants.CACHE_KEY_DISTINCT_ORDER + "17133183897154258932503","test");
	}

	public JSONObject actionForTestCache () {
		EasyCache cache = CacheManager.getMemcache();
		String s = "u0BVx1u07+iLxGdDoyC3dA==";
		String o = cache.get("u0BVx1u07+iLxGdDoyC3dA==");
		return EasyResult.ok(StringUtils.contains(s,o));
	}



	public JSONObject actionForTest0007() {
		OrderHotspotUtil.getInstance().loadData(getEntId(), this.getDbName());
		return EasyResult.ok();
	}

	public JSONObject actionForTest0006() {
		try {
			String command = getPara("command");
			String startDate = getPara("startDate");
			String endDate = getPara("endDate");
			String provinceCode = getPara("provinceCode");
			String schema = this.getDbName();
			String entId = this.getEntId();

			JSONObject provinceCfg = ClassTools.getClass(OrderProvinceCfgService.class).getProvinceCfg(schema, provinceCode, "0");
			String timeStyleId = provinceCfg.getString("TIME_STYLE_ID");
			if("workDays".equals(command)) {
				int days = WorkTimeUtils.getWorkDays(this.getEntId(), this.getDbName(), timeStyleId, startDate, endDate);
				return EasyResult.ok(days);
			}
			return EasyResult.fail("command");
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return EasyResult.fail();
	}

	public JSONObject actionForTest0005() {
		try {
			EasySQL sql = new EasySQL("select ID,ORDER_ID,CONTENT");
			sql.append("from " + this.getTableName("C_BO_ORDER_FOLLOW") + " t1");
			sql.appendLike("UNHANDLE_REASON\\\":\\\"14\\\"", "where CONTENT like ?");
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			for(JSONObject row : list) {
				String id = row.getString("ID");
				String orderId = row.getString("ORDER_ID");
				JSONObject content = row.getJSONObject("CONTENT");
				if(content != null) {
					JSONObject appealOrder = content.getJSONObject("c_box_appeal_order");
					if(appealOrder != null) {
						appealOrder.put("UNHANDLE_REASON", "35号令属性-6");

						logger.info("content:" + JSONObject.toJSONString(content));

						EasyRecord record = new EasyRecord(this.getTableName("C_BO_ORDER_FOLLOW"), "ID");
						record.put("ID", id);
						record.put("CONTENT", JSONObject.toJSONString(content));
						this.getQuery().update(record);
					}

				}
			}
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return EasyResult.fail();
	}

	public JSONObject actionForTest0004() {
		try {
			List<JSONObject> provinceList = this.getQuery().queryForList("select * from CC_PROVINCE", new Object[]{}, new JSONMapperImpl());
			for (JSONObject province : provinceList) {
				OrderNodeUtil.getInstance().reloadNode(Constants.APPEAL_FLOW_KEY, this.getEntId(), province.getString("PROVINCE_CODE"), this.getDbName());
				OrderNodeUtil.getInstance().reloadNode(Constants.MEDIATE_FLOW_KEY, this.getEntId(), province.getString("PROVINCE_CODE"), this.getDbName());
				OrderNodeUtil.getInstance().reloadNode(Constants.DUTY_FLOW_KEY, this.getEntId(), province.getString("PROVINCE_CODE"), this.getDbName());
				logger.info("更新缓存[" +  province.getString("PROVINCE_NAME") + "]成功");
			}
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return EasyResult.fail();
	}

	public JSONObject actionForTest0003() {
		for(int i = 0; i < 20; i++) {
			String entId = this.getEntId();
			new Thread(()->{
				String cacheKey = "XTY_ORDER_HOSPOT_PREFIX_" + entId;
				logger.info("thread cacheAll");
				for(int j = 0; j < 20; j++) {
					try {
						RedisUtil.hgetAll(cacheKey);
					} catch (Exception e) {
					}
					logger.info("thread getCacheAll:" + j);
				}
			}).start();
			logger.info("start thread" + i + "成功");
		}
		return EasyResult.ok();
	}

	public JSONObject actionForTest0002() {
		try {
			ClassTools.getClass(AILLMService.class).reload();
		} catch (Exception e) {

		}

		return EasyResult.ok();
	}

	public JSONObject actionForTest0001() {
		try {
			EasyQuery query = this.getQuery();
			query.setMaxRow(10000);

			List<JSONObject> list = query.queryForList("select EX_JSON from " + this.getTableName("xty_order_import_record"), new Object[] {}, new JSONMapperImpl());
			for (JSONObject json : list) {
				JSONObject exJson = json.getJSONObject("EX_JSON");

				query.execute("update " + this.getTableName("C_BOX_APPEAL_ORDER") + " set IMPORT_ENT_NAME = ? where SECTION_NO = ?", exJson.getString("entName"), exJson.getString("serialNo"));
			}
			query.setMaxRow(3000);
		} catch (Exception e) {

		}

		return EasyResult.ok();
	}

	*//**
	 * 大模型工单摘要
	 * @return
	 *//*
	public JSONObject actionForTest001() {
		String orderId = this.getPara("orderId");
		String appealContent = this.getPara("appealContent");
		if(StringUtils.isBlank(orderId)) {
			orderId = "16983859780802780376134";
		}
		if(StringUtils.isBlank(appealContent)) {
			appealContent = "我想投诉话费缴纳清单清楚，存在恶意扣费的情况";
		}

		new AILLMReqService().orderSummary(UserUtil.getUser(getRequest()), orderId, appealContent, false);
		return EasyResult.ok();
	}


	*//**
	 * 大模型工单摘要
	 * @return
	 *//*
	public JSONObject actionForTest002() {
		String orderId = this.getPara("orderId");
		String appealContent = this.getPara("appealContent");
		if(StringUtils.isBlank(orderId)) {
			orderId = "16983859780802780376134";
		}
		if(StringUtils.isBlank(appealContent)) {
			appealContent = "我想投诉话费缴纳清单清楚，存在恶意扣费的情况";
		}

		new AILLMReqService().orderHotspot(UserUtil.getUser(getRequest()), orderId, appealContent, false);
		return EasyResult.ok();
	}


	*//**
	 * 大模型重复申告
	 * @return
	 *//*
	public JSONObject actionForTest003() {
		String orderId = this.getPara("orderId");
		String appealContent = this.getPara("appealContent");
		if(StringUtils.isBlank(orderId)) {
			orderId = "16983859780802780376134";
		}
		if(StringUtils.isBlank(appealContent)) {
			appealContent = "我想投诉话费缴纳清单清楚，存在恶意扣费的情况";
		}

		List<JSONObject> megerList = new ArrayList<JSONObject>();

		JSONObject order1 = new JSONObject();
		order1.put("ID", "12312312412412");
		order1.put("APPEAL_CONTENT", "我怀疑运营商对我恶意扣费,但是我没证据");
		megerList.add(order1);

		JSONObject order2 = new JSONObject();
		order2.put("ID", "123123124123123123");
		order2.put("APPEAL_CONTENT", "特么的运营商肯定对我恶意扣费,但是我没证据");
		megerList.add(order2);
		//new AILLMReqService().repeatTemplate(UserUtil.getUser(getRequest()), orderId, appealContent, megerList, false);
		return EasyResult.ok();
	}

	*//**
	 * 大模型重复申告
	 * @return
	 *//*
	public JSONObject actionForTest004() {
		String orderId = this.getPara("orderId");
		String appealContent = this.getPara("appealContent");
		if(StringUtils.isBlank(orderId)) {
			orderId = "16983859780802780376134";
		}
		if(StringUtils.isBlank(appealContent)) {
			appealContent = "我想投诉话费缴纳清单清楚，存在恶意扣费的情况";
		}

		new AILLMReqService().categoryCode(UserUtil.getUser(getRequest()), orderId, appealContent, false);
		return EasyResult.ok();
	}

	*//**
	 * 大模型重复申告
	 * @return
	 *//*
	public JSONObject actionForTest005() {
		String orderId = this.getPara("orderId");
		String appealContent = this.getPara("appealContent");
		if(StringUtils.isBlank(orderId)) {
			orderId = "16983859780802780376134";
		}
		if(StringUtils.isBlank(appealContent)) {
			appealContent = "我想投诉话费缴纳清单清楚，存在恶意扣费的情况";
		}

		new AILLMReqService().businessCode(UserUtil.getUser(getRequest()), orderId, appealContent, false);
		return EasyResult.ok();
	}

	*//**
	 * 推送历史申诉记录分类码
	 * @return
	 *//*
	public JSONObject actionForTest006() {
		String dateId = this.getPara("dateId");
		if(StringUtils.isBlank(dateId)) {
			dateId = DateUtil.getCurrentDateStr("yyyy-MM-dd");
		}

		try {
			EasySQL sql = new EasySQL("select M_ID,APPEAL_CONTENT");
			sql.append("from " + this.getTableName("C_BOX_APPEAL_ORDER"));
			sql.append("where 1=1");
			sql.append(dateId + " 00:00:00", "and APPEAL_TIME >= ?");
			sql.append(dateId + " 23:59:59", "and APPEAL_TIME <= ?");
			// sql.append("and (CLASS_CODE3 is null or CLASS_CODE3 = '')");
			// sql.append("and (HOT_TAG is null or HOT_TAG = '')");
			sql.append("order by APPEAL_TIME");
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

			AILLMReqService aillmReqService = new AILLMReqService();
			UserModel user = UserUtil.getUser(getRequest());
			for (JSONObject row : list) {
				String orderId = row.getString("M_ID");
				String appealContent = row.getString("APPEAL_CONTENT");
				aillmReqService.businessCode(user, orderId, appealContent, false);
				aillmReqService.categoryCode(user, orderId, appealContent, false);
				aillmReqService.orderHotspot(user, orderId, appealContent, false);
				aillmReqService.orderSummary(user, orderId, appealContent, false);
			}
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return EasyResult.fail();
	}


    *//**
     * 添加个人视图配置表
     * @return
     *//*
	public JSONObject actionForTest01() {
		try {
			UserModel user = UserUtil.getUser(getRequest());

			JSONObject param = this.getJSONObject();
			param.put("busiId", "cs001");
			param.put("entId", user.getEpCode());
			param.put("busiOrderId", user.getBusiOrderId());
			param.put("schema", user.getSchemaName());
			param.put("fileName", "头像.jpg");
			param.put("fileContent", "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");

			return CustServer.getInstance().uploadFile(param);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}


	public JSONObject actionForTest02() {
		try {
			JSONObject param = this.getJSONObject();
			param.put("dateId",getJSONObject().getString("dateId"));
			return ServiceUtil.invoke2("XTY_AREA_STAT",param);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public JSONObject actionForTest03() {
		try {
			JSONObject param = this.getJSONObject();
			param.put("dateId",getJSONObject().getString("dateId"));
			return ServiceUtil.invoke2("XTY_ENT_STAT",param);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public JSONObject actionForTest04() {
		try {
			JSONObject param = this.getJSONObject();
			param.put("dateId",getJSONObject().getString("dateId"));
			return ServiceUtil.invoke2("XTY_CLASS_STAT",param);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public JSONObject actionForTest07() {
		try {
			logger.info("actionForTest07");
			JSONObject param = this.getJSONObject();
			return ServiceUtil.invoke2("XTY_AUTO_NODE_STAT",param);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public JSONObject actionForTest05() {
		try {
			JSONObject param = this.getJSONObject();
			return ServiceUtil.invoke2("XTY_AUTO_STAT_OVERTIME",param);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public JSONObject actionForTest06() {
		try {
			JSONObject param = this.getJSONObject();
			return ServiceUtil.invoke2("XTY_AUTO_COMPLETE",param);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	//



	public JSONObject actionForTest08() {
		try {
			JSONObject param = this.getJSONObject();
			return ServiceUtil.invoke2("XTY_AUTO_ACCEPT",param);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public JSONObject actionForTest09() {
		try {
			JSONObject param = this.getJSONObject();
			return ServiceUtil.invoke2("XTY_AUTO_AUDIT",param);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public JSONObject actionForTest10() {
		return EasyResult.ok(AppContext.getContext(Constants.BASE_APP_NAME).getProperty("MAX_EXPORT_SIZE","5000"));
	}

	//ORDER_CHECK_SERVICE

	public JSONObject actionForReStart() {
		try {
			JSONObject param = this.getJSONObject();
			param.put("command","create");
			return ServiceUtil.invoke2("ORDER_CHECK_SERVICE",param);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public JSONObject actionForRedel() {
		try {
			JSONObject param = this.getJSONObject();
			param.put("command","del");
			return ServiceUtil.invoke2("ORDER_CHECK_SERVICE",param);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}


	public JSONObject actionForReStat() {
		try {
			AutoReStatTimeService aClass = ClassTools.getClass(AutoReStatTimeService.class);
			aClass.invoke();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public JSONObject actionForGeneralNextTask () {
		ClassTools.getClass(OrderExtendTodoService.class).addExtendTodo(Constants.APPEAL_FLOW_KEY, "e2dbfdf6d6b04795bc1b481a2bcd3b77", "17116653649386977057189", "7c5046e762d54ab2b068c3cf861e2366", getEntId(), getBusiOrderId(),"lvchao",getDbName(), false);
		BusiOrderService busiOrderService =  ClassTools.getClass(BusiOrderService.class);
		busiOrderService.updateBusiData(getEntId(),getBusiOrderId(),"17116653649386977057189",Constants.APPEAL_FLOW_KEY,"7c5046e762d54ab2b068c3cf861e2366",null);
		return EasyResult.ok();
	}

	public JSONObject actionForTestWorkDay () {
		int workDays = WorkTimeUtils.getWorkDays(getEntId(), getDbName(), "82940918245243140389364", "2024-04-08 00:00:00", "2024-04-12 23:59:59");
		return EasyResult.ok(workDays);
	}

	public JSONObject actionForStatWorkDays () throws ServiceException {
		ServiceUtil.invoke2("XTY_AUTO_STAT_OVERTIME",null);
		*//*while (ThreadMgr.isStart()) {
			ServiceUtil.invoke2("XTY_AUTO_STAT_OVERTIME",null);
			//CommonUtil.sleep(5*60);
		}*//*
		return EasyResult.ok();
	}

	public JSONObject actionForReStartImport() {
		try {
			JSONObject param = this.getJSONObject();
			param.put("batchId","17098023204631551416368");
			param.put("command","import");
			logger.info("-->[创建部申诉工单请求] param:" + param.toJSONString());
			IService service = ServiceContext.getService("XTY_AUTO_IMPORT_ORDER");
			JSONObject result = service.invoke(param);
			logger.info("-->[创建部申诉工单返回] result:" + result.toJSONString());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public JSONObject actionForExtractAffirm() {
		try {
			JSONObject param = new JSONObject();
			param.put("command","execProvinceFixSample");
			String startDateStr = this.getPara("startDate");
			String endDateStr = this.getPara("endDate");
			if (StringUtils.isAnyBlank(startDateStr,endDateStr)) {
				return EasyResult.fail();
			}
            Date endDate = DateUtil.getDate(DateUtil.TIME_FORMAT_YMD, endDateStr);
            while (!DateUtil.getDate(DateUtil.TIME_FORMAT_YMD, startDateStr).after(endDate)) {
            	param.put("dealDateStr",startDateStr);
            	logger.info("抽取申诉时间 ： " + startDateStr + "进行认定：" + JSONObject.toJSONString(param));
				JSONObject result = ServiceUtil.invoke2("FIX_DUTY_SERVICE", param);
				logger.info("服务结果：" + JSONObject.toJSONString(result));
                startDateStr = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD,startDateStr,1);
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public JSONObject actionForGenerateDeptConfig () {
		try {
			EasyQuery query = this.getQuery();
			EasySQL sql = new EasySQL();
			sql.append("select * from ycbusi_ekf.cc_skill_group t1 where t1.GROUP_TYPE like '990203%' and t1.SKILL_GROUP_TYPE ='struct' and GROUP_TYPE !='99020305'");
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if (CollectionUtils.isNotEmpty(list)) {
				String entId = getEntId();
				String busiOrderId = getBusiOrderId();
				String currentDateStr = DateUtil.getCurrentDateStr();
				list.forEach(item -> {
					try {
						String skillGroupCode = item.getString("SKILL_GROUP_CODE");
						String groupType = item.getString("GROUP_TYPE");
						EasyRecord record = new EasyRecord(getTableName("xty_dept_config"),"ID");
						record.put("ID",IDGenerator.getDefaultNUMID());
						record.put("ENT_ID", entId);
						record.put("BUSI_ORDER_ID", busiOrderId);
						record.put("CREATE_TIME", currentDateStr);
						record.put("CREATE_ACC","system");
						record.put("DEPT_CODE",skillGroupCode);
						record.put("GROUP_TYPE",groupType);
						record.put("CONFIG_TYPE","1");
						record.put("CONFIG_JSON","{\n" +
								"  \"append\": {\n" +
								"      \"ALLOCATION_TYPE\": \"03\",\n" +
								"      \"IS_ALLOCATION_LEADER\": \"\",\n" +
								"      \"DEAL_DEPT_TYPE\": \"990203\",\n" +
								"      \"DEAL_DAY_TYPE\": \"1\",\n" +
								"      \"ALLOW_STATUS\": \"02\",\n" +
								"      \"NODE_ID\": \"append\",\n" +
								"      \"DISTRIBUTE_TYPE\": \"\",\n" +
								"      \"SORT_INDEX\": \"1\",\n" +
								"      \"ASSOCIATION_NODE\": \"\",\n" +
								"      \"STATUS\": \"01\",\n" +
								"      \"NODE_NAME\": \"提交材料\",\n" +
								"      \"DEAL_DAYS\": \"1000\",\n" +
								"      \"DEPT_TYPE\": \"990202\"\n" +
								"    },\n" +
								"\t\"judgement\":{\n" +
								"      \"ALLOCATION_TYPE\": \"03\",\n" +
								"      \"IS_ALLOCATION_LEADER\": \"\",\n" +
								"      \"DEAL_DEPT_TYPE\": \"990203\",\n" +
								"      \"DEAL_DAY_TYPE\": \"1\",\n" +
								"      \"ALLOW_STATUS\": \"02\",\n" +
								"      \"NODE_ID\": \"judgement\",\n" +
								"      \"DISTRIBUTE_TYPE\": \"\",\n" +
								"      \"SORT_INDEX\": \"2\",\n" +
								"      \"ASSOCIATION_NODE\": \"\",\n" +
								"      \"STATUS\": \"01\",\n" +
								"      \"NODE_NAME\": \"初判\",\n" +
								"      \"DEAL_DAYS\": \"1000\",\n" +
								"      \"DEPT_TYPE\": \"990202\"\n" +
								"    },\n" +
								"\t\"judgement_check\":{\n" +
								"      \"ALLOCATION_TYPE\": \"03\",\n" +
								"      \"IS_ALLOCATION_LEADER\": \"\",\n" +
								"      \"DEAL_DEPT_TYPE\": \"\",\n" +
								"      \"DEAL_DAY_TYPE\": \"1\",\n" +
								"      \"ALLOW_STATUS\": \"02\",\n" +
								"      \"NODE_ID\": \"judgement_check\",\n" +
								"      \"DISTRIBUTE_TYPE\": \"\",\n" +
								"      \"SORT_INDEX\": \"3\",\n" +
								"      \"ASSOCIATION_NODE\": \"\",\n" +
								"      \"STATUS\": \"01\",\n" +
								"      \"NODE_NAME\": \"初判审核\",\n" +
								"      \"DEAL_DAYS\": \"1000\",\n" +
								"      \"DEPT_TYPE\": \"\"\n" +
								"    }\n" +
								"}");
						query.save(record);

					}catch (Exception e) {}
				});
			}
		}catch (Exception e) {

		}
		return EasyResult.ok();
	}

	public JSONObject actionForTestReceipt () {
		String json = "{\"receipts\":[{\"err\":\"0\",\"sub_seq\":\"0\",\"report_time\":\"2024-05-20 13:21:54\",\"mobile\":\"13438860271\",\"fail_desc\":\"DELIVRD\",\"msg_id\":\"0000000804930917\",\"corp_id\":\"xx0112\"}],\"serialId\":\"17161825259396935830645\",\"command\":\"saveSmsReceipt\"}";
		JSONObject jsonObject = JSONObject.parseObject(json);
		CustServer.getInstance().saveSmsReceipt(jsonObject);
		return EasyResult.ok();
	}

	public JSONObject actionForGenerateEntConfig () {
		try {
			EasyQuery query = this.getQuery();
			EasySQL sql = new EasySQL();
			sql.append("select * from ycbusi_ekf.cc_skill_group t1 where t1.GROUP_TYPE like '990105%' and t1.SKILL_GROUP_TYPE ='struct'");
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if (CollectionUtils.isNotEmpty(list)) {
				String entId = getEntId();
				String busiOrderId = getBusiOrderId();
				String currentDateStr = DateUtil.getCurrentDateStr();
				list.forEach(item -> {
					try {
						String skillGroupCode = item.getString("SKILL_GROUP_CODE");
						String groupType = item.getString("GROUP_TYPE");
						EasyRecord record = new EasyRecord(getTableName("xty_dept_config"),"ID");
						record.put("ID",IDGenerator.getDefaultNUMID());
						record.put("ENT_ID", entId);
						record.put("BUSI_ORDER_ID", busiOrderId);
						record.put("CREATE_TIME", currentDateStr);
						record.put("CREATE_ACC","system");
						record.put("DEPT_CODE",skillGroupCode);
						record.put("GROUP_TYPE",groupType);
						record.put("CONFIG_TYPE","1");
						record.put("CONFIG_JSON","{\n" +
								"  \"USERTASK_x2xx8d4m9l\": {\n" +
								"    \"ALLOCATION_TYPE\": \"01\",\n" +
								"    \"IS_ALLOCATION_LEADER\": \"01\",\n" +
								"    \"DEAL_DAYS\": \"10\",\n" +
								"    \"DEAL_DEPT_TYPE\": \"990203\",\n" +
								"    \"ASSOCIATION_PROC_ID\": \"xty_ss\",\n" +
								"    \"DEAL_DAY_TYPE\": \"1\",\n" +
								"    \"DISTRIBUTE_TYPE\": \"02\",\n" +
								"    \"ASSOCIATION_NODE\": \"USERTASK_a9mbgnr9tp\"\n" +
								"  },\n" +
								"  \"USERTASK_amgsih6wi8\": {\n" +
								"    \"ALLOCATION_TYPE\": \"01\",\n" +
								"    \"IS_ALLOCATION_LEADER\": \"01\",\n" +
								"    \"DEAL_DAYS\": \"10\",\n" +
								"    \"DEAL_DEPT_TYPE\": \"990203\",\n" +
								"    \"ASSOCIATION_PROC_ID\": \"xty_tj\",\n" +
								"    \"DEAL_DAY_TYPE\": \"1\",\n" +
								"    \"DISTRIBUTE_TYPE\": \"02\",\n" +
								"    \"ASSOCIATION_NODE\": \"USERTASK_q9a0mrmzhi\"\n" +
								"  },\n" +
								"  \"USERTASK_q9a0mrmzhi\": {\n" +
								"    \"ALLOCATION_TYPE\": \"01\",\n" +
								"    \"IS_ALLOCATION_LEADER\": \"01\",\n" +
								"    \"DEAL_DAYS\": \"2\",\n" +
								"    \"DEAL_DEPT_TYPE\": \"990203\",\n" +
								"    \"ASSOCIATION_PROC_ID\": \"xty_ss\",\n" +
								"    \"DEAL_DAY_TYPE\": \"2\",\n" +
								"    \"DISTRIBUTE_TYPE\": \"02\",\n" +
								"    \"ASSOCIATION_NODE\": \"USERTASK_x2xx8d4m9l\"\n" +
								"  },\n" +
								"  \"USERTASK_a9mbgnr9tp\": {\n" +
								"    \"ALLOCATION_TYPE\": \"01\",\n" +
								"    \"IS_ALLOCATION_LEADER\": \"01\",\n" +
								"    \"DEAL_DAYS\": \"3\",\n" +
								"    \"DEAL_DEPT_TYPE\": \"990203\",\n" +
								"    \"DEAL_DAY_TYPE\": \"2\",\n" +
								"    \"DISTRIBUTE_TYPE\": \"01\",\n" +
								"    \"ASSOCIATION_NODE\": \"\"\n" +
								"  }\n" +
								"}");
						query.save(record);

					}catch (Exception e) {}
				});
			}
		}catch (Exception e) {

		}
		return EasyResult.ok();
	}

	public JSONObject actionForUpdateDeptConfig () {
		try {
			Map map = new HashMap();
			map.put("USERTASK_a9mbgnr9tp","预处理");
			map.put("USERTASK_amgsih6wi8","省企业处理");
			map.put("USERTASK_q9a0mrmzhi","省企业调解答复");
			map.put("USERTASK_x2xx8d4m9l","处理");
			EasySQL sql = new EasySQL();
			sql.append(" select * from " + getTableName("xty_dept_config"));
			sql.append(" where 1=1 ");
			sql.append(" and GROUP_TYPE = '99020305'");
			sql.append(" or GROUP_TYPE like '990105%'");
			EasyQuery query = this.getQuery();
			logger.info("sql:" + sql.getFullSq());
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if (CollectionUtils.isNotEmpty(list)) {
				list.stream().forEach(item -> {
					try {
						JSONObject config_json = item.getJSONObject("CONFIG_JSON");
						if (Objects.nonNull(config_json)) {
							config_json.keySet().stream().forEach(e -> {
								JSONObject jsonObject = config_json.getJSONObject(e);
								jsonObject.put("NODE_ID",e);
								jsonObject.put("NODE_NAME",map.get(e));
							});
							item.put("CONFIG_JSON",config_json.toJSONString());
						}
						EasyRecord record = new EasyRecord(getTableName("xty_dept_config"),"ID");
						record.putAll(item);
						query.update(record);
					} catch (Exception e){
						logger.error(e.getMessage(),e);
					}
				});
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return EasyResult.ok();
	}

		public JSONObject actionForDelMultiTask () {
		try {
			EasyQuery query = this.getQuery();
			EasySQL sql = new EasySQL();
			sql.append("select t1.* ,t2.P_ORDER_NO  from ycbusi_ekf.xty_ru_task t1 left join ycbusi_ekf.c_box_duty_order t2 on t2.ID = t1.M_ID  where NODE_ID != 'affirm_check' and t1.M_ID in (SELECT M_ID  FROM ycbusi_ekf.xty_ru_task x where  FLOW_KEY ='xty_rd' group by M_ID having count(M_ID) >1) order by M_ID ");
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			Map<String, List<JSONObject>> collect = list.stream().collect(Collectors.groupingBy(item -> item.getString("M_ID")));
			logger.info(JSONObject.toJSONString(collect));
			collect.keySet().stream().forEach(mId -> {
				List<JSONObject> jsonObjects = collect.get(mId);
				if (CollectionUtils.isNotEmpty(jsonObjects) && jsonObjects.size() >= 2) {
					try {
						JSONObject object = jsonObjects.get(0);
						String id = object.getString("ID");
						if (StringUtils.isNotBlank(id)) {
							EasyRecord record = new EasyRecord(getTableName("xty_ru_task"),"ID");
							record.put("ID",id);
							query.deleteById(record);
						}
					} catch (Exception e) {

					}
				}
			});
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return EasyResult.ok();
	}

	public JSONObject actionForGenerateProvinceConfig () {
		try {
			EasyQuery query = this.getQuery();
			EasySQL sql = new EasySQL();
			sql.append("SELECT x.* FROM ycbusi_ekf.xty_province_config x where CONFIG_TYPE = '0' and PROVINCE is not null");
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if (CollectionUtils.isNotEmpty(list)) {
				list.forEach(item -> {
					try {
						EasyRecord record = new EasyRecord(getTableName("xty_province_config"),"ID");
						JSONObject config = item.getJSONObject("CONFIG_JSON");
						JSONObject dutyConfig = new JSONObject();
						dutyConfig.put("SPECIALIZED_CLASS_DEAL_TIMES",1000);
						dutyConfig.put("SPECIALIZED_CLASS_DEAL_TIME_TYPE","1");
						dutyConfig.put("SPECIALIZED_CLASS_SAMPLE_RATIO",10);
						dutyConfig.put("PROVINCE_MANAGER_DEAL_TIMES",1000);
						dutyConfig.put("PROVINCE_MANAGER_SAMPLE_RATIO",100);
						dutyConfig.put("PROVINCE_MANAGER_DEAL_TIME_TYPE","1");
						config.put("FIX_DUTY_CONFIG",dutyConfig);
						record.put("ID",item.getString("ID"));
						record.put("CONFIG_JSON",config.toJSONString());
						query.update(record);

					}catch (Exception e) {
						logger.error(e.getMessage(),e);
					}
				});
			}
		}catch (Exception e) {

		}
		return EasyResult.ok();
	}

	public JSONObject actionForAddDuty () throws SQLException {
		EasyQuery query = this.getQuery();
		String entId = getEntId();
		String schema = getDbName();
		String busiOrderId = getBusiOrderId();
		String taskId = DateUtil.getCurrentDateStr("yyyyMMddHHmmss");
		EasySQL sql = new EasySQL("select t1.ID,t1.PROVINCE_CODE,t1.CITY_CODE,t1.ENT_TYPE,t1.P_ID,t1.APPEAL_TIME ");
		sql.append("from " + schema + ".C_BOX_DUTY_ORDER t1");
		sql.append("left join " + schema + ".XTY_RU_TASK t2 on t1.ID = t2.M_ID").append(Constants.DUTY_FLOW_KEY, "and FLOW_KEY = ?").append(Constants.DUTY_PROCESS_NODE_AFFIRM, "and NODE_ID = ?");
		sql.append("where 1=1");
		sql.append(entId, "and t1.ENT_ID = ?");
		sql.append(Constants.AFFIRM_STATUS_9, "and t1.FIX_STATUS = ?");
		sql.append("02", "and t1.JUDGEMENT_RESULT = ? ");
		sql.append(" and t2.ID is null");
		sql.append(" and t1.P_ORDER_NO in ('部-20231200022108')");
		logger.info("抽取SQL：" + sql.getFullSq());
		List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		if (CollectionUtils.isNotEmpty(list) ) {
			list.stream().forEach(item -> {
				try {
					String provinceCode = item.getString("PROVINCE_CODE");
					// 获取定责节点配置
					JSONObject nodeJson = OrderNodeUtil.getInstance().getNodeInfo(Constants.DUTY_FLOW_KEY, Constants.DUTY_PROCESS_NODE_AFFIRM, entId, provinceCode, schema);
					if(nodeJson == null) {
						logger.error("省份[" + provinceCode + "]配置信息不全，无法分配");
						throw new Exception("省份[" + provinceCode + "]配置信息不全，无法分配");
					}
					nodeJson.put("PROCESS_ID", Constants.DUTY_FLOW_KEY);
					setUserByOrder(entId, busiOrderId, schema, taskId + provinceCode, nodeJson, item, Constants.AFFIRM_STATUS_9);
				} catch (Exception e) {
					logger.error(e.getMessage(),e);
				}
			});
		}
		return  EasyResult.ok();
	}

	public JSONObject actionForAddAffirmCheck () throws SQLException {
		EasyQuery query = this.getQuery();
		String entId = getEntId();
		String schema = getDbName();
		String busiOrderId = getBusiOrderId();
		String taskId = DateUtil.getCurrentDateStr("yyyyMMddHHmmss");
		EasySQL sql = new EasySQL("select t1.ID,t1.PROVINCE_CODE,t1.CITY_CODE,t1.ENT_TYPE,t1.P_ID,t1.APPEAL_TIME ");
		sql.append("from " + schema + ".C_BOX_DUTY_ORDER t1");
		sql.append("left join " + schema + ".XTY_RU_TASK t2 on t1.ID = t2.M_ID").append(Constants.DUTY_FLOW_KEY, "and FLOW_KEY = ?").append(Constants.DUTY_PROCESS_NODE_AFFIRM_CHECK, "and NODE_ID = ?");
		sql.append("where 1=1");
		sql.append(" and t1.PROVINCE_CODE ='210000'");
		sql.append(" and t1.FIX_STATUS ='5'");
		//sql.append(" and t1.P_ORDER_NO = '部-20240100152021'");
		logger.info("抽取SQL：" + sql.getFullSq());
		List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		if (CollectionUtils.isNotEmpty(list)) {
			list.stream().forEach(item -> {
				try {
					String provinceCode = item.getString("PROVINCE_CODE");
					// 获取定责节点配置
					JSONObject nodeJson = OrderNodeUtil.getInstance().getNodeInfo(Constants.DUTY_FLOW_KEY, Constants.DUTY_PROCESS_NODE_AFFIRM_CHECK, entId, provinceCode, schema);
					if(nodeJson == null) {
						logger.error("省份[" + provinceCode + "]配置信息不全，无法分配");
						throw new Exception("省份[" + provinceCode + "]配置信息不全，无法分配");
					}
					nodeJson.put("PROCESS_ID", Constants.DUTY_FLOW_KEY);
					setUserByOrder(entId, busiOrderId, schema, taskId + provinceCode, nodeJson, item);
				} catch (Exception e) {
					logger.error(e.getMessage(),e);
				}
			});
		}
		return  EasyResult.ok();
	}


	public JSONObject actionForDoneOrder() {
		try {
			String order = this.getPara("order");
			EasySQL sql = new EasySQL();
			sql.append(order, "select EX_JSON ->> '$.appealResult' APPEAL_RESULT,EX_JSON ->> '$.unHandleReason' REASON,CREATE_ACC,CREATE_NAME from " + this.getDbName() + ".xty_order_import_record where SERIAL_NO = ? and `STATUS` = 7 order by CREATE_TIME desc LIMIT 1", false);
			JSONObject importOrder = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

			if (importOrder == null) {
				return EasyResult.error();
			}

			EasySQL idSql = new EasySQL();
            idSql.append(order, "select m_id from " + this.getDbName() + ".c_box_appeal_order where SECTION_NO = ?", false);
            String orderId = this.getQuery().queryForString(idSql.getSQL(), idSql.getParams());

            if (StringUtils.isBlank(orderId)) {
                return EasyResult.error();
            }

            String appealResult = importOrder.getString("APPEAL_RESULT");
            String reason = importOrder.getString("REASON");
            String userAcc = importOrder.getString("CREATE_ACC");
            String username = importOrder.getString("CREATE_NAME");

            reason = this.getDict(this.getEntId(), "XTY_UNACCEPT_REASON", reason);
			appealResult = this.getDict(this.getEntId(), "XTY_APPEAL_RESULT", appealResult);

			OrderExtendTodoService todoService = ClassTools.getClass(OrderExtendTodoService.class);

			EasySQL orderSql = new EasySQL();
            orderSql.append("select t1.ID ORDER_ID, t1.ORDER_NO, t1.PROC_KEY, t1.PROC_INST_ID,t1.CURR_TASK_ID, t2.*");
            orderSql.append("from "+ this.getDbName() +".C_BO_BASE_ORDER t1");
            orderSql.append("left join "+ this.getDbName() +".C_BOX_APPEAL_ORDER t2 on t2.M_ID=t1.ID");
            orderSql.append("where 1=1");
            orderSql.append(orderId, "and t1.ID=?", false);
			EasyQuery query = QueryFactory.getWriteQuery();
			JSONObject appealOrder =  query.queryForRow(orderSql.getSQL(), orderSql.getParams(), new JSONMapperImpl());

			EasySQL taskSql = new EasySQL();
			taskSql.append(orderId, "select TASK_ID from " + this.getDbName() + ".XTY_RU_TASK" + " where M_ID = ?",false);
            String taskId = query.queryForString(taskSql.getSQL(), taskSql.getParams());
            String procInstId = appealOrder.getString("PROC_INST_ID");
			String assignee = null;
            String processKey = "xty_ss";
            String entId = this.getEntId();
            String busiOrderId = this.getBusiOrderId();
            String schema = this.getDbName();
            String status = Constants.XTY_SERVICE_STATUS_06;

			if (StringUtils.isNotBlank(taskId)) {
				logger.info("扩展操作参数：" + processKey + "," + procInstId + "," + orderId + "," + taskId + "," + entId + "," + busiOrderId + "," + assignee);
				todoService.addExtendTodo(processKey, procInstId, orderId, taskId, entId, busiOrderId, assignee, schema, false);
				OrderUtils.cleanBaseInfo(orderId, schema);
			}

			// 执行办结操作，更新字段
            EasyRecord record = new EasyRecord(schema + ".C_BOX_APPEAL_ORDER", "M_ID");
            record.set("M_ID", orderId);
            record.set("IS_COMPLETED", Constants.MAGIC_01);
            record.set("COMPLETE_TIME", DateUtil.getCurrentDateStr());
            record.set("SERVICE_STATUS", status);
            record.set("APPEAL_RESULT", appealResult);
            record.set("UNHANDLE_REASON2", reason);
			if (this.getQuery().update(record)) {
				logger.info("办结成功，record：" + record.toJSONString());
			}

            JSONObject content = new JSONObject();
            content.put("SERVICE_STATUS", status);
            content.put("APPEAL_RESULT", appealResult);
            content.put("UNHANDLE_REASON2", reason);

            OrderUtils.addFlow(entId, busiOrderId, processKey, orderId,
                    Constants.XTY_SERVICE_STATUS_06, "导入办结", content.toJSONString(), userAcc, username);
		}catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return EasyResult.ok();
	}

	private String getDict(String entId, String dictCode, String entName) {
		try {
			JSONObject listByGroupCode = DictCache.getJsonEnableDictListByGroupCode(entId, dictCode);
			JSONObject data = listByGroupCode.getJSONObject("data");
			Map<Object, String> map = data.entrySet().stream().collect(Collectors.toMap(i -> i.getValue(), i -> i.getKey(), (o1, o2) -> o2));
			return map.get(entName);
		} catch (Exception e) {

		}
		return "";
	}

	private void setUserByOrder(String entId ,String busiOrderId , String schema, String taskId, JSONObject nodeJson, JSONObject orderJson, String fixStatus) throws Exception {
		String orderId = orderJson.getString("ID");
		String handleAcc = null;
		String handleName = null;
		String handleDeptCode = "";
		String handleDeptName = "";
		String id = RandomKit.randomStr();
		if(StringUtils.isBlank(handleAcc)) {
			JSONObject assignUser = getAssignUser(entId, busiOrderId, orderId, id, Constants.DUTY_FLOW_KEY, nodeJson);
			if(assignUser == null) {
				throw new Exception("未找到分配人员");
			}
			handleName = assignUser.getString("userName");
			handleAcc = assignUser.getString("userAcc");
			handleDeptCode = assignUser.getString("userDeptCode");
			handleDeptName = assignUser.getString("userDeptName");
		}

		logger.info("定责工单[" + orderId + "]分配处理人员[" + handleAcc + "]处理部门[" + handleDeptCode + "]处理部门名称[" + handleDeptName + "]");
		EasyRecord record = new EasyRecord(schema + ".XTY_RU_TASK", "ID");

		record.put("ID", id);
		record.put("TASK_ID",taskId);
		record.put("M_ID", orderId);
		record.put("NODE_ID", nodeJson.getString("NODE_ID"));
		record.put("NODE_NAME", nodeJson.getString("NODE_NAME"));
		record.put("PROVINCE", orderJson.getString("PROVINCE_CODE"));
		record.put("CITY", orderJson.getString("CITY_CODE"));
		record.put("DEPT_TYPE", nodeJson.getString("DEAL_DEPT_TYPE"));
		record.put("DEPT_CODE", handleDeptCode);
		record.put("DEPT_NAME", handleDeptName);
		record.put("ASSIGN_TYPE", nodeJson.getString("ALLOCATION_TYPE"));
		record.put("STATUS", "01");
		record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
		record.put("OPTION_TIME", DateUtil.getCurrentDateStr());
		record.put("FLOW_KEY", nodeJson.getString("PROCESS_ID"));
		record.put("HANDLE_ACC", handleAcc);
		record.put("HANDLE_NAME", handleName);
		JSONObject provinceCfg = ClassTools.getClass(OrderProvinceCfgService.class).getProvinceCfg(schema, orderJson.getString("PROVINCE_CODE"), "0");
		String timeStyleId = provinceCfg.getString("TIME_STYLE_ID");
		String dealDayType = nodeJson.getString("DEAL_DAY_TYPE");
		String dealDays = nodeJson.getString("DEAL_DAYS");
		String appealTime = orderJson.getString("APPEAL_TIME");
		record.put("PLAN_TIME", ClassTools.getClass(OrderTimeService.class).getPlanDealTime(entId, busiOrderId, orderId, timeStyleId, StringUtils.isNotBlank(appealTime) ? appealTime : DateUtil.getCurrentDateStr(), dealDayType, CommonUtil.parseInt(dealDays)));
		this.getQuery().save(record);

		if(StringUtils.equalsAny(fixStatus, Constants.AFFIRM_STATUS_4, Constants.AFFIRM_STATUS_9)) {
			this.getQuery().execute("update " + schema + ".C_BOX_DUTY_ORDER set FIX_STATUS = ? where ID = ?", Constants.AFFIRM_STATUS_4, orderId);
		} else if(StringUtils.equals(fixStatus, Constants.AFFIRM_STATUS_5)){
			this.getQuery().execute("update " + schema + ".C_BOX_DUTY_ORDER set FIX_STATUS = ? where ID = ?", Constants.AFFIRM_STATUS_6, orderId);
		}

		saveRecordLog(entId, schema, nodeJson.getString("PROCESS_ID"), id, null, orderId, nodeJson.getString("NODE_ID"), Constants.ASSIGN_RESULT_1);
	}

	private void setUserByOrder(String entId ,String busiOrderId , String schema, String taskId, JSONObject nodeJson, JSONObject orderJson) throws Exception {
		String orderId = orderJson.getString("ID");
		String handleAcc = null;
		String handleName = null;
		String handleDeptCode = "";
		String handleDeptName = "";
		String id = RandomKit.randomStr();
		if(StringUtils.isBlank(handleAcc)) {
			JSONObject assignUser = getAssignUser(entId, busiOrderId, orderId, id, Constants.DUTY_FLOW_KEY, nodeJson);
			if(assignUser == null) {
				throw new Exception("未找到分配人员");
			}
			handleName = assignUser.getString("userName");
			handleAcc = assignUser.getString("userAcc");
			handleDeptCode = assignUser.getString("userDeptCode");
			handleDeptName = assignUser.getString("userDeptName");
		}
		logger.info("定责工单[" + orderId + "]分配处理人员[" + handleAcc + "]处理部门[" + handleDeptCode + "]处理部门名称[" + handleDeptName + "]");
		EasyRecord record = new EasyRecord(schema + ".XTY_RU_TASK", "ID");
		record.put("ID", id);
		record.put("TASK_ID",taskId);
		record.put("M_ID", orderId);
		record.put("NODE_ID", nodeJson.getString("NODE_ID"));
		record.put("NODE_NAME", nodeJson.getString("NODE_NAME"));
		record.put("PROVINCE", orderJson.getString("PROVINCE_CODE"));
		record.put("CITY", orderJson.getString("CITY_CODE"));
		record.put("DEPT_TYPE", nodeJson.getString("DEAL_DEPT_TYPE"));
		record.put("DEPT_CODE", handleDeptCode);
		record.put("DEPT_NAME", handleDeptName);
		record.put("ASSIGN_TYPE", nodeJson.getString("ALLOCATION_TYPE"));
		record.put("STATUS", "01");
		record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
		record.put("OPTION_TIME", DateUtil.getCurrentDateStr());
		record.put("FLOW_KEY", nodeJson.getString("PROCESS_ID"));
		record.put("HANDLE_ACC", handleAcc);
		record.put("HANDLE_NAME", handleName);
		JSONObject provinceCfg = ClassTools.getClass(OrderProvinceCfgService.class).getProvinceCfg(schema, orderJson.getString("PROVINCE_CODE"), "0");
		String timeStyleId = provinceCfg.getString("TIME_STYLE_ID");
		String dealDayType = nodeJson.getString("DEAL_DAY_TYPE");
		String dealDays = nodeJson.getString("DEAL_DAYS");
		String appealTime = orderJson.getString("APPEAL_TIME");
		record.put("PLAN_TIME", ClassTools.getClass(OrderTimeService.class).getPlanDealTime(entId, busiOrderId, orderId, timeStyleId, StringUtils.isNotBlank(appealTime) ? appealTime : DateUtil.getCurrentDateStr(), dealDayType, CommonUtil.parseInt(dealDays)));
		this.getQuery().save(record);
		this.getQuery().execute("update " + schema + ".C_BOX_DUTY_ORDER set FIX_STATUS = ? where ID = ?", Constants.AFFIRM_STATUS_4, orderId);
	}

	public JSONObject getAssignUser(String entId,String busiOrderId,String orderId,String taskId,String flowKey,JSONObject nodeConfig) {
		String allocationType = nodeConfig.getString("ALLOCATION_TYPE");
		AllocationEnum item = AllocationEnum.getItem(allocationType);
		if (Objects.isNull(item)) {
			return new JSONObject();
		}
		AllocationStrategy strategy = item.getStrategy();
		JSONObject allocation = strategy.allocation(entId, busiOrderId, orderId,taskId, flowKey, nodeConfig);
		logger.info("获取分配结果：" + JSONObject.toJSONString(allocation));
		return allocation;
	}

	private void saveRecordLog(String entId, String schema, String processId, String taskId, String appealOrderId, String dutyOrderId, String nodeId, String assginResult) {
		try {
			String logId = this.getQuery().queryForString("select ID from " + schema + ".XTY_TASK_ALLOCATION_RECORD where DUTY_ORDER_ID = ? and PROFESSOR_ID = ? and NODE_ID = ?", dutyOrderId, processId, nodeId);

			EasyRecord record = new EasyRecord(schema + ".XTY_TASK_ALLOCATION_RECORD", "ID");
			record.put("PROFESSOR_ID", processId);
			record.put("TASK_ID", taskId);
			record.put("APPEAL_ORDER_ID", appealOrderId);
			record.put("DUTY_ORDER_ID", dutyOrderId);
			record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			record.put("NODE_ID", nodeId);
			record.put("ASSIGN_RESULT", assginResult);
			if(StringUtils.isNotBlank(logId)) {
				record.put("ID", logId);
				this.getQuery().update(record);
			} else {
				record.put("ID", RandomKit.randomStr());
				this.getQuery().save(record);
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}*/

	/*public JSONObject actionForFixData() {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" select t1.EX_JSON,t2.ID from "+getTableName("xty_order_import_record")+" t1");
			sql.append(" left join "+getTableName("c_box_appeal_order")+" t2 on t2.M_ID  = t1.ORDER_ID ");
			sql.append(" where 1=1 ");
			sql.append(" and t1.IMPORT_TYPE ='02' and t2.ID is not null ");
			sql.append(" order by t2.ID desc");
			int pageNo = 1;
			int pageSize = 3000;
			boolean flag = false;
			List<Object[]> data = new ArrayList<>();
			String entId = getEntId();
			EasyQuery query = this.getQuery();
			String updatesql = "update " + getTableName("c_box_appeal_order") + " set APPEAL_NAME = ? ,PHONE = ? where ID = ? ";
			while (true && pageNo < 30000) {
				List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), pageNo, pageSize, new JSONMapperImpl());
				if (CommonUtil.listIsNull(list)) {
					flag = true;
				} else if (list.size() < 3000) {
					flag = true;
				}
				list.stream().forEach(item -> {
					JSONObject exJson = item.getJSONObject("EX_JSON");
					if (Objects.nonNull(exJson)) {
						Object[] val = new Object[3];
						val[0] = PhoneCryptor.getInstance().encrypt(entId, exJson.getString("appealName"));
						val[1] = PhoneCryptor.getInstance().encrypt(entId, exJson.getString("phone"));
						val[2] = item.getString("ID");
						data.add(val);
					}
					if (data.size() % 300 == 0) {
						try {
							query.executeBatch(updatesql,data);
							data.clear();
						} catch (SQLException throwables) {
							throwables.printStackTrace();
						}

					}

				});
				if (flag) {
					if (data.size() > 0) {
						try {
							query.executeBatch(updatesql,data);
							data.clear();
						} catch (SQLException throwables) {
							throwables.printStackTrace();
						}
					}
					break;
				}
				pageNo++;
			}

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public JSONObject actionForFixLastData () {
		try {
			EasyQuery query = this.getQuery();
			String entId = this.getEntId();
			EasySQL sql = new EasySQL();
			sql.append(" select * from "+getTableName("c_box_appeal_order")+" where 1=1 ");
			sql.appendIn(new String[]{"17053131137224067490451","17053134645265122508539","17053700173566564330845","17053703206569974647104","17057112539899938716942","17057114677357045827739","17064019531817755253214","17067504394640653929230"}," and ID ");
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if (CollectionUtils.isNotEmpty(list)) {
				list.stream().forEach(item -> {
					try {
						EasyRecord record = new EasyRecord(getTableName("c_box_appeal_order"),"ID");
						record.put("ID",item.getString("ID"));
						record.put("APPEAL_NAME",PhoneCryptor.getInstance().encrypt(entId, item.getString("APPEAL_NAME")));
						record.put("PHONE",PhoneCryptor.getInstance().encrypt(entId, item.getString("PHONE")));
						query.update(record);
					} catch (Exception e) {

					}
				});
			}
		} catch (Exception e) {

		}
		return EasyResult.ok();
	}*/

	public static void main(String[] args) {
		System.out.println(fileToBase64("C://Users//ZhengJ//OneDrive//桌面//QQ图片20230330154519.jpg"));
	}

	//文件转base64
	public static String fileToBase64(String fileName) {
		String base64String = "";
        try {
            File file = new File(fileName);
            FileInputStream fileInputStream = new FileInputStream(file);
            byte[] bytes = new byte[(int) file.length()];
            fileInputStream.read(bytes);
            base64String = Base64.getEncoder().encodeToString(bytes);
            fileInputStream.close();
        } catch (IOException e) {
        	e.printStackTrace();
        }
        return base64String;

	}

	public JSONObject actionForRloadConfig(){
		try {
			EasyQuery query = getQuery();
			EasySQL sql = new EasySQL("select t1.* " +
					"from " + getTableName("XTY_PROVINCE_CONFIG") + " t1");
			sql.append(" where 1=1");
			sql.appendIn(new String[]{"xty_tj","xty_ss"}," and t1.CONFIG_TYPE  ");
			List<JSONObject> list = query.queryForList(sql.getSQL(),sql.getParams(), new JSONMapperImpl());
			// 循环处理每个省份的配置
			list.stream().forEach(item -> {
				try {
					reloadConfig(item,item.getString("PROVINCE"),UserUtil.getUser(getRequest()));
				} catch (SQLException throwables) {
					logger.error(throwables.getMessage(), throwables);
				}
			});
		} catch (	Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	private void reloadConfig(JSONObject configData, String provinceCode, UserModel user) throws SQLException {
		logger.info("开始重新加载省份[" + provinceCode + "]的配置");
		String entId = user.getEpCode();
		String userDept = user.getDeptCode();
		String busiOrderId = user.getBusiOrderId();
		String configId = configData == null ? "" : configData.getString("ID");
		List<JSONObject> oldNodeList = new ArrayList<JSONObject>();
		if(StringUtils.isNotBlank(configId)) {
			oldNodeList = configData.getJSONObject("CONFIG_JSON").getJSONArray("orderNodeList").toJavaList(JSONObject.class);
		}
		Map<String, JSONObject> oldNodeMap = oldNodeList.stream().collect(Collectors.toMap(t -> t.getString("NODE_ID"), t -> t,(o1,o2) -> o1));
		List<JSONObject> nodeList = new ArrayList<JSONObject>();
		String processId = configData.getString("CONFIG_TYPE");
		// 其他流程从工单流程定义获取
		nodeList = this.getQuery().queryForList("select NODE_ID,NODE_NAME,SORT_INDEX from " + this.getTableName("C_WF_PROCESS_NODE") + " where FLOW_KEY = ? and ENT_ID = ? and BUSI_ORDER_ID = ?  AND NODE_TYPE not in ('branch','start','end','receiveTask') and NODE_ID not in ('USERTASK_z9cry6t8t4','USERTASK_qpm35i8u1') order by SORT_INDEX asc",
				new Object[] {processId, entId, busiOrderId}, new JSONMapperImpl());
		for (JSONObject nodeJson : nodeList) {
			String nodeId = nodeJson.getString("NODE_ID");
			String nodeName = nodeJson.getString("NODE_NAME");
			JSONObject oldNodeData = oldNodeMap.get(nodeId);
			JSONObject row = new JSONObject();
			if(oldNodeData != null) {
				row = JSONObject.parseObject(oldNodeData.toJSONString());
				row.put("NODE_NAME", nodeName);
				row.put("SORT_INDEX", nodeJson.getString("SORT_INDEX"));
				if(nodeId.startsWith("pre")) {
					row.put("ALLOW_STATUS", DictConstants.ENABEL_STATUS_ENABLE);
				} else {
					row.put("ALLOW_STATUS", DictConstants.ENABEL_STATUS_DISENABLE);
				}
			} else {
				switch (nodeId) {
					case Constants.MEDIATE_PROVINCE_DEAL_CHECK:
						JSONObject object = oldNodeMap.get(Constants.MEDIATE_PROVINCE_DEAL);
						if (Objects.nonNull(object)) {
							row.put("DEPT_TYPE", object.getString("DEPT_TYPE"));
							// 处理日期类型 1-自然日 2-工作日
							row.put("DEAL_DAY_TYPE", object.getString("DEAL_DAY_TYPE"));
							// 处理天数
							row.put("DEAL_DAYS", object.getString("DEAL_DAYS"));
							// 是否分配组长
							row.put("IS_ALLOCATION_LEADER", object.getString("IS_ALLOCATION_LEADER"));
							row.put("SORT_INDEX", 3);
						}
						break;
					case Constants.MEDIATE_ENT_CONFIRM_CHECK:
						JSONObject object1 = oldNodeMap.get(Constants.MEDIATE_ENT_CONFIRM);
						if (Objects.nonNull(object1)) {
							row.put("DEPT_TYPE", object1.getString("DEPT_TYPE"));
							// 处理日期类型 1-自然日 2-工作日
							row.put("DEAL_DAY_TYPE", object1.getString("DEAL_DAY_TYPE"));
							row.put("DEAL_DAYS", object1.getString("DEAL_DAYS"));
							row.put("IS_ALLOCATION_LEADER", object1.getString("IS_ALLOCATION_LEADER"));
							row.put("SORT_INDEX", 5);
						}
						break;
					case Constants.MEDIATE_ENT_DEAL_CHECK:
						JSONObject object2 = oldNodeMap.get(Constants.MEDIATE_ENT_DEAL);
						if (Objects.nonNull(object2)) {
							row.put("DEPT_TYPE", object2.getString("DEPT_TYPE"));
							// 处理日期类型 1-自然日 2-工作日
							row.put("DEAL_DAY_TYPE", object2.getString("DEAL_DAY_TYPE"));
							row.put("DEAL_DAYS", object2.getString("DEAL_DAYS"));
							row.put("IS_ALLOCATION_LEADER", object2.getString("IS_ALLOCATION_LEADER"));
							row.put("SORT_INDEX", 7);
						}
						break;
					case Constants.MEDIATE_PROVINCE_CONFIRM_CHECK:
						JSONObject object3 = oldNodeMap.get(Constants.MEDIATE_PROVINCE_CONFIRM);
						if (Objects.nonNull(object3)) {
							row.put("DEPT_TYPE", object3.getString("DEPT_TYPE"));
							// 处理日期类型 1-自然日 2-工作日
							row.put("DEAL_DAY_TYPE", object3.getString("DEAL_DAY_TYPE"));
							row.put("DEAL_DAYS", object3.getString("DEAL_DAYS"));
							row.put("IS_ALLOCATION_LEADER", object3.getString("IS_ALLOCATION_LEADER"));
							row.put("SORT_INDEX", 9);
						}
						break;
					case Constants.COMPLAIN_PREACCEPT_CHECK:
						JSONObject object4 = oldNodeMap.get(Constants.COMPLAIN_PREACCEPT);
						if (Objects.nonNull(object4)) {
							row.put("DEPT_TYPE", object4.getString("DEPT_TYPE"));
							// 处理日期类型 1-自然日 2-工作日
							row.put("DEAL_DAY_TYPE", object4.getString("DEAL_DAY_TYPE"));
							row.put("DEAL_DAYS", object4.getString("DEAL_DAYS"));
							row.put("IS_ALLOCATION_LEADER", object4.getString("IS_ALLOCATION_LEADER"));
							row.put("SORT_INDEX", 3);
						}
						break;
					case Constants.COMPLAIN_ACCEPT_CHECK:
						JSONObject object5 = oldNodeMap.get(Constants.COMPLAIN_ACCEPT);
						if (Objects.nonNull(object5)) {
							row.put("DEPT_TYPE", object5.getString("DEPT_TYPE"));
							// 处理日期类型 1-自然日 2-工作日
							row.put("DEAL_DAY_TYPE", object5.getString("DEAL_DAY_TYPE"));
							row.put("DEAL_DAYS", object5.getString("DEAL_DAYS"));
							row.put("IS_ALLOCATION_LEADER", object5.getString("IS_ALLOCATION_LEADER"));
							row.put("SORT_INDEX", 5);
						}
						break;
					case Constants.COMPLAIN_DEAL_CHECK:
						JSONObject object6 = oldNodeMap.get(Constants.COMPLAIN_DEAL);
						if (Objects.nonNull(object6)) {
							row.put("DEPT_TYPE", object6.getString("DEPT_TYPE"));
							// 处理日期类型 1-自然日 2-工作日
							row.put("DEAL_DAY_TYPE", object6.getString("DEAL_DAY_TYPE"));
							row.put("DEAL_DAYS", object6.getString("DEAL_DAYS"));
							row.put("IS_ALLOCATION_LEADER", object6.getString("IS_ALLOCATION_LEADER"));
							row.put("SORT_INDEX", 7);
						}
						break;
					case Constants.COMPLAIN_AUDIT_CHECK:
						JSONObject object7 = oldNodeMap.get(Constants.COMPLAIN_AUDIT);
						if (Objects.nonNull(object7)) {
							row.put("DEPT_TYPE", object7.getString("DEPT_TYPE"));
							// 处理日期类型 1-自然日 2-工作日
							row.put("DEAL_DAY_TYPE", object7.getString("DEAL_DAY_TYPE"));
							row.put("DEAL_DAYS", object7.getString("DEAL_DAYS"));
							row.put("IS_ALLOCATION_LEADER", object7.getString("IS_ALLOCATION_LEADER"));
							row.put("SORT_INDEX", 9);
						}
						break;
					default:
						break;
				}
				row.put("NODE_ID", nodeId);
				row.put("NODE_NAME", nodeName);
				row.put("ALLOCATION_TYPE", Constants.ORDER_NODE_ALLOCATION_TYPE_04);
				//row.put("DISTRIBUTE_TYPE", Constants.ORDER_NODE_DISTRIBUTE_TYPE_01);
				row.put("DEAL_DEPT_TYPE", "");
				row.put("ASSOCIATION_NODE", "");
				row.put("STATUS", DictConstants.ENABEL_STATUS_DISENABLE);
			}
			oldNodeMap.put(nodeId, row);
		}

		EasyRecord record = new EasyRecord(this.getTableName("XTY_PROVINCE_CONFIG"), "ID");
		List<JSONObject> list = oldNodeMap.values().stream().sorted(Comparator.comparing(t -> t.getIntValue("SORT_INDEX"))).collect(Collectors.toList());
		JSONObject configJson = new JSONObject();
		configJson.put("orderNodeList", list);

		record.put("CONFIG_JSON", JSONObject.toJSONString(configJson));
		if(StringUtils.isBlank(configId)) {
			configId = RandomKit.randomStr();
			record.put("ID", configId);
			record.put("ENT_ID", entId);
			record.put("BUSI_ORDER_ID", busiOrderId);
			record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			record.put("CREATE_ACC", user.getUserAcc());
			record.put("CREATE_DEPT", userDept);
			record.put("PROVINCE", provinceCode);
			record.put("CONFIG_TYPE", processId);
			this.getQuery().save(record);
		} else {
			record.put("ID", configId);
			record.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
			record.put("UPDATE_ACC", user.getUserAcc());
			this.getQuery().update(record);
		}
		logger.info("省份配置：" + record.toString());
		logger.info("重新加载省份[" + provinceCode + "]的流程配置成功！");
		OrderNodeUtil.getInstance().reloadNode(processId, entId, provinceCode, this.getDbName());
		CacheUtil.delete("XTY_PROVINCE_CONFIG_" + provinceCode + "_" + processId);
	}


	public JSONObject actionForRepairData() {
        try {
			EasySQL sql = new EasySQL();
			sql.append(" select *  from ycbusi_ekf.c_box_appeal_order cbao where ENT_TYPE ='990105' and CREATE_TIME >='2025-07-16 00:00:00' ");
			logger.info("修复数据sql：" + sql.getFullSq());
			List<JSONObject> rows = getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			for(JSONObject jsonObject : rows) {
				String id = jsonObject.getString("M_ID");
				String entType = getGroupType(jsonObject.getString("ENT_DEPT_CODE"));
				EasyRecord record = new EasyRecord(getTableName("c_box_appeal_order"),"M_ID");
				record.put("M_ID", id);
				record.put("ENT_TYPE", entType);
				record.put("DEPT_TYPE", entType);
				//record.put("ENT_DEPT_NAME", PhoneCryptor.getInstance().encrypt(jsonObject.getString("ENT_DEPT_NAME")));
				logger.info("更新工单：" + JSONObject.toJSONString(record));
				getQuery().update(record);
				
				if ("01".equals(jsonObject.getString("SERVICE_STATUS"))) {
					JSONObject appealOrder = OrderUtils.getAppealOrder(id, getDbName());
					logger.info("工单：" + JSONObject.toJSONString(appealOrder));
					JSONObject task = getTask(id);
					if(Objects.nonNull(task)) {
						//根据工单的省份、工单环节，查询省份的配置信息
						String flowKey = task.getString("FLOW_KEY");
						String nodeId = task.getString("NODE_ID");
						JSONObject provinceCfgJson = ClassTools.getClass(OrderProvinceCfgService.class)
								.getProvinceOrderNodeCfg(getDbName(), task.getString("PROVINCE"),flowKey, nodeId);
						logger.info("省份配置：" + JSONObject.toJSONString(provinceCfgJson));
						OrderNodeUtil.getFullNodeInfo(provinceCfgJson, appealOrder, getDbName(), nodeId, flowKey);
						if (provinceCfgJson != null) {
							JSONObject assignUser = this.getAssignUser(getEntId(), getBusiOrderId(), id, id, flowKey, task.getString("CREATE_TIME"), provinceCfgJson, null);
							EasyRecord taskrEasyRecord = new EasyRecord(getTableName("xty_ru_task"),"ID");
							taskrEasyRecord.put("ID", task.getString("ID"));
							taskrEasyRecord.put("HANDLE_ACC", assignUser.getString("HANDLE_ACC"));
							taskrEasyRecord.put("HANDLE_NAME", assignUser.getString("HANDLE_NAME"));
							taskrEasyRecord.put("DEPT_CODE", assignUser.getString("DEPT_CODE"));
							taskrEasyRecord.put("DEPT_NAME", assignUser.getString("DEPT_NAME"));
							logger.info("更新任务：" + JSONObject.toJSONString(taskrEasyRecord));
							getQuery().update(taskrEasyRecord);
						}
					}
				}
				
			}
		} catch (Exception e) {
            logger.error("修复数据异常", e);
        }
        return EasyResult.ok();

    }

	 public JSONObject actionForReStartFlow () {
		String flowKey = "xty_ss";
		String operType = "add";
		String ruTaskId = "";
		UserModel user = UserUtil.getUser(this.getRequest());
		EasyQuery query = this.getQuery();
		try {
			String startTime =   "2025-07-16 00:00:00";
			EasySQL sql = new EasySQL();
			//select t2.* from ycbusi_ekf.c_box_mediate_order t1 left join ycbusi_ekf.c_bo_base_order t2 on t2.ID = t1.M_ID  where t1.CREATE_TIME >='2025-06-24 08:41:42'
			sql.append("select t1.*  from ycbusi_ekf.c_box_appeal_order t1 left join ycbusi_ekf.c_bo_base_order t2 on t2.ID = t1.M_ID  ");
			sql.append(startTime,"where t1.CREATE_TIME >= ?");
			sql.append(" and t2.ID='82472613833539555591315'");
			CommonLogger.getLogger().info(sql.getFullSq());
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if (CollectionUtils.isNotEmpty(list)) {
				list.forEach(jsonObject -> {
					try {
						String orderId = jsonObject.getString("M_ID");
						JSONObject appealJson = this.getQuery().queryForRow("select * from " + this.getTableName("C_BOX_APPEAL_ORDER") + " where M_ID = ?", new Object[]{orderId}, new JSONMapperImpl());
						JSONObject baseJson = this.getQuery().queryForRow("select ID, ORDER_NO, PROC_INST_ID, PROC_KEY  from " + this.getTableName("C_BO_BASE_ORDER") + " where ID = ?", new Object[]{orderId}, new JSONMapperImpl());
					
						String provinceCode = appealJson.getString("PROVINCE_CODE");
						JSONObject data = new JSONObject();
						removeEmptyFields(appealJson);
						data.put("c_box_appeal_order", appealJson);
						data.put("c_bo_base_order", baseJson);

						ImportDataService.getInstance().startFlow(orderId, flowKey, operType, ruTaskId, "system", provinceCode, data, user,new JSONObject(),true,appealJson.getString("ORG_CODE"));
					} catch (Exception e) {
						logger.info("err:",e);

					}
				});}
			
		} catch (Exception e) {
			logger.info("err:",e);
		}
		return EasyResult.ok();
	}
 

	 public static void removeEmptyFields(JSONObject jsonObject) {
        // 使用entrySet()获取所有的键值对，并遍历
        Iterator<Map.Entry<String, Object>> iterator = jsonObject.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = iterator.next();
            Object value = entry.getValue();
            // 检查值是否为空，这里以null和空字符串为例
            if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
                iterator.remove(); // 移除空值字段
            } else if (value instanceof JSONObject) {
                // 如果值是另一个JSONObject，则递归调用此方法
                removeEmptyFields((JSONObject) value);
            }
            // 对于其他类型的值（如数组、数字等），可以根据需要添加额外的检查
        }
    }

	 /**
     * 获取任务处理人员信息
     * @param entId
     * @param busiOrderId
     * @param orderId 工单ID
     * @param taskId xty任务ID
     * @param flowKey 流程key
     * @param dateStr 创建时间
     * @param provinceCfgJson 省份配置信息
     * @param allocation 分配到人信息
     * @return
     */
    private JSONObject getAssignUser (String entId,String busiOrderId,String orderId,String taskId,
                                      String flowKey,String dateStr,JSONObject provinceCfgJson,JSONObject allocation) {
        JSONObject handleInfo = new JSONObject();
        JSONObject assignUser = ClassTools.getClass(OrderService.class).getAssignUser(entId, busiOrderId, orderId, taskId, flowKey, provinceCfgJson,allocation);
        String allocationType = provinceCfgJson.getString("ALLOCATION_TYPE");
        String deptType = provinceCfgJson.getString("DEAL_DEPT_TYPE"); //获取部门类型
        String dealDayType = provinceCfgJson.getString("DEAL_DAY_TYPE");

        handleInfo.put("DEPT_TYPE", deptType);
        handleInfo.put("ASSIGN_TYPE", allocationType);
        handleInfo.put("DAY_TYPES", dealDayType);
        if (assignUser != null) {
            handleInfo.put("DEPT_CODE", assignUser.getString("userDeptCode"));
            handleInfo.put("DEPT_NAME", assignUser.getString("userDeptName"));
            handleInfo.put("HANDLE_ACC", assignUser.getString("userAcc"));
            handleInfo.put("HANDLE_NAME", assignUser.getString("userName"));
        }
        dateStr = StringUtils.isNotBlank(assignUser.getString("allocationTime"))? assignUser.getString("allocationTime") : dateStr;
        handleInfo.put("OPTION_TIME", StringUtils.isNotBlank(handleInfo.getString("HANDLE_ACC")) ? dateStr : "");
        handleInfo.put("STATUS", StringUtils.isNotBlank(handleInfo.getString("HANDLE_ACC")) ? Constants.MAGIC_02 :  Constants.MAGIC_01);
        return handleInfo;
    }

    private String getGroupType (String deptCode) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append(" select GROUP_TYPE from ycbusi_ekf.CC_SKILL_GROUP where 1=1 ");
        sql.append(deptCode," and SKILL_GROUP_CODE = ? ");
        return getQuery().queryForString(sql.getSQL(),sql.getParams());
    }

	private JSONObject getTask (String mId) throws SQLException {
		EasySQL sql = new EasySQL();
		sql.append("select * from  " + getTableName("xty_ru_task") +
				" where FLOW_KEY ='xty_ss'   " );
		sql.append(mId," and M_ID = ? ");
		sql.append("USERTASK_a9mbgnr9tp"," and NODE_ID = ? " );
		logger.info("task sql:" + sql.getFullSq());
		return this.getQuery().queryForRow(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
	}

}
