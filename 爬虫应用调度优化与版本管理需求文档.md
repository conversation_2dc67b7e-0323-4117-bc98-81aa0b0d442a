# 爬虫应用调度优化与版本管理需求文档

## 1. 项目概述

### 1.1 项目背景
目前系统中运行着一个爬虫应用，该应用通过定时调度机制在每月的1日、11日、21日凌晨2点执行数据爬取任务。随着业务发展，现有的固定调度模式已无法满足业务需求，需要增加灵活的自定义爬取功能，并完善版本管理和数据处理机制。

### 1.2 项目目标
- 提供灵活的爬虫调度机制，支持自定义爬取日期
- 建立完善的版本管理系统，支持版本作废和数据清理
- 实现自动化的重新稽核机制
- 优化公示率和未公示资费数据的统计处理

### 1.3 项目范围
- 爬虫调度系统优化
- 版本管理功能开发
- 数据清理机制实现
- 重新稽核流程设计
- 统计数据重新计算功能

## 2. 需求分析

### 2.1 现状分析
**当前系统特点：**
- 固定调度：每月1、11、21日凌晨2点执行
- 版本生成：每次爬取生成一个版本
- 数据处理：基于版本进行数据管理

**存在问题：**
- 调度时间固定，无法满足业务灵活性需求
- 缺乏版本作废和数据清理机制
- 版本间数据关联处理不完善
- 重新稽核流程缺失

### 2.2 业务需求
1. **调度灵活性**：支持用户自定义爬取日期
2. **版本管理**：支持版本查看、作废操作
3. **数据清理**：版本作废时自动清理相关数据
4. **重新稽核**：版本作废后触发相关时间段的重新稽核
5. **统计更新**：重新计算公示率和未公示资费数据

## 3. 功能需求

### 3.1 爬虫调度管理

#### 3.1.1 固定调度保持
- **功能描述**：保持现有每月1、11、21日凌晨2点的固定调度
- **实现要求**：
  - 维持现有调度逻辑不变
  - 确保固定调度的稳定性和可靠性

#### 3.1.2 自定义调度
- **功能描述**：用户可以自定义指定爬取日期
- **功能要求**：
  - 支持单次爬取任务的日期指定
  - 支持批量日期设置
  - 提供日期冲突检测（避免重复爬取同一日期）
  - 支持立即执行和定时执行两种模式
- **界面要求**：
  - 提供日历选择器
  - 显示已爬取日期标识
  - 提供批量操作功能

#### 3.1.3 调度状态管理
- **功能描述**：管理和监控爬虫任务执行状态
- **状态类型**：
  - 待执行：已设置但未开始执行
  - 执行中：正在进行爬取
  - 执行成功：爬取完成并生成版本
  - 执行失败：爬取过程中出现错误
  - 已取消：用户主动取消的任务

### 3.2 版本管理系统

#### 3.2.1 版本信息展示
- **功能描述**：展示所有爬虫版本的详细信息
- **展示内容**：
  - 版本号/版本ID
  - 爬取日期
  - 爬取时间
  - 数据量统计
  - 版本状态（正常/已作废）
  - 操作记录

#### 3.2.2 版本作废功能
- **功能描述**：支持对指定版本进行作废操作
- **操作流程**：
  1. 用户选择要作废的版本
  2. 系统提示作废影响范围
  3. 用户确认作废操作
  4. 系统执行作废流程
- **权限控制**：
  - 只有授权用户可以执行作废操作
  - 记录作废操作的用户和时间
- **影响范围提示**：
  - 显示将被清理的数据范围
  - 显示需要重新稽核的日期范围
  - 显示受影响的统计数据

### 3.3 数据清理机制

#### 3.3.1 版本数据清理
- **功能描述**：版本作废时自动清理对应版本的所有数据
- **清理范围**：
  - 爬取的原始数据
  - 处理后的业务数据
  - 相关的统计数据
  - 版本关联的配置信息
- **清理策略**：
  - 物理删除：彻底删除数据
  - 逻辑删除：标记为已删除状态
  - 备份清理：清理前进行数据备份

#### 3.3.2 关联数据处理
- **功能描述**：处理与被作废版本相关的其他数据
- **处理内容**：
  - 更新数据关联关系
  - 清理缓存数据
  - 更新索引信息
  - 清理临时文件

### 3.4 重新稽核机制

#### 3.4.1 稽核范围确定
- **功能描述**：确定需要重新稽核的日期范围
- **范围计算规则**：
  - 起始日期：被作废版本的爬取日期
  - 结束日期：下一个有效版本的爬取日期（不包含）
  - 如果是最新版本被作废，则稽核到当前日期

#### 3.4.2 自动稽核触发
- **功能描述**：版本作废后自动触发重新稽核流程
- **触发机制**：
  - 数据清理完成后自动启动
  - 支持异步处理，避免阻塞用户操作
  - 提供稽核进度监控

#### 3.4.3 稽核结果处理
- **功能描述**：处理重新稽核的结果数据
- **处理内容**：
  - 更新稽核结果数据
  - 生成稽核报告
  - 触发后续统计计算

### 3.5 统计数据重新计算

#### 3.5.1 公示率重新计算
- **功能描述**：基于重新稽核结果计算公示率
- **计算范围**：受影响日期范围内的所有公示率数据
- **计算内容**：
  - 总体公示率
  - 分类公示率
  - 趋势分析数据

#### 3.5.2 未公示资费数据更新
- **功能描述**：更新未公示资费相关统计数据
- **更新内容**：
  - 未公示资费数量
  - 未公示资费金额
  - 未公示资费分布
  - 相关报表数据

## 4. 非功能性需求

### 4.1 性能要求
- 爬虫任务执行时间：单次爬取任务不超过2小时
- 版本作废处理时间：不超过30分钟
- 重新稽核处理时间：根据数据量确定，提供进度显示
- 页面响应时间：不超过3秒

### 4.2 可靠性要求
- 系统可用性：99.5%以上
- 数据一致性：确保版本作废和重新稽核过程中的数据一致性
- 错误恢复：提供完善的错误处理和恢复机制

### 4.3 安全性要求
- 操作权限控制：版本作废等关键操作需要权限验证
- 操作日志记录：记录所有关键操作的详细日志
- 数据备份：重要操作前进行数据备份

### 4.4 可维护性要求
- 代码规范：遵循项目编码规范
- 文档完整：提供完整的技术文档和用户手册
- 监控告警：提供系统运行状态监控和异常告警

## 5. 业务流程

### 5.1 自定义爬取流程
```
用户选择日期 → 系统验证日期 → 创建爬取任务 → 执行爬取 → 生成版本 → 更新统计数据
```

### 5.2 版本作废流程
```
选择版本 → 确认作废 → 清理版本数据 → 确定稽核范围 → 执行重新稽核 → 重新计算统计数据 → 完成作废
```

### 5.3 重新稽核流程
```
确定稽核范围 → 准备稽核数据 → 执行稽核算法 → 生成稽核结果 → 更新相关数据 → 生成稽核报告
```

## 6. 技术要求

### 6.1 系统架构
- 使用消息队列处理异步任务
- 采用分布式锁确保并发安全

### 6.2 数据库设计
- 版本信息表：存储版本基本信息和状态，已有表结构，需要调整列；
- 调度任务表：存储自定义调度任务信息，需设计；
- 操作日志表：记录关键操作日志

### 6.3 接口设计
- RESTful API设计风格
- 统一的响应格式和错误码
- 完善的接口文档和测试用例

## 7. 验收标准

### 7.1 功能验收
- [ ] 自定义爬取功能正常工作
- [ ] 版本管理界面完整展示
- [ ] 版本作废功能正确执行
- [ ] 数据清理机制有效运行
- [ ] 重新稽核流程自动触发
- [ ] 统计数据正确更新

### 7.2 性能验收
- [ ] 满足性能要求指标
- [ ] 并发处理能力达标
- [ ] 系统稳定性验证

### 7.3 安全验收
- [ ] 权限控制有效
- [ ] 操作日志完整
- [ ] 数据安全保障

## 8. 项目计划



## 9. 风险评估

### 9.1 技术风险
- 大数据量处理性能风险
- 并发操作数据一致性风险
- 系统集成兼容性风险

### 9.2 业务风险
- 版本作废对业务连续性的影响
- 重新稽核结果的准确性风险
- 用户操作错误的风险

### 9.3 风险应对
- 充分的性能测试和优化
- 完善的数据备份和恢复机制
- 详细的用户操作指南和培训

---

