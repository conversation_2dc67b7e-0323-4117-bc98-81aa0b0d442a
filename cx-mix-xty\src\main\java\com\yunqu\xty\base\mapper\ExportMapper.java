package com.yunqu.xty.base.mapper;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.RoleModel;
import com.yq.busi.common.model.UserModel;
import com.yunqu.xty.utils.CryptorRightUtil;
import com.yunqu.xty.utils.PhoneCryptor;
import com.yunqu.xty.utils.excel.ExcelExportDataHandle;
import org.apache.commons.collections4.CollectionUtils;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.log.JDBCLogger;
import org.easitline.common.utils.string.StringUtils;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ExportMapper implements EasyRowMapper<JSONObject> {

    private ExcelExportDataHandle handle;

    private UserModel userModel;

    private String modelCode;

    public ExportMapper(ExcelExportDataHandle handle,UserModel user,String modelCode) {
        this.handle = handle;
        this.userModel = user;
        this.modelCode = modelCode;
    }

    /**
     * 匹配setter方法的正则表达式
     */

    @Override
    public JSONObject mapRow(ResultSet rs, int convertField) {
        try {

            ResultSetMetaData meta = rs.getMetaData();
            int columnCount = meta.getColumnCount();
            JSONObject json = new JSONObject(true);
            for (int i = 0; i < columnCount; ++i) {
                String key = meta.getColumnLabel(i + 1).toUpperCase();
                Object object = rs.getObject(key);
                if (Objects.nonNull(object)) {
//                    if (handle != null) {
//                        object = handle.handle(key,String.valueOf(object));
//                    }
                    if (filterField(userModel, key)) {
                        object = PhoneCryptor.getInstance().decrypt(String.valueOf(object));
                    }
                    json.put(key, object);
                } else {
                    json.put(key, "");
                }
            }
            return json;
        } catch (Exception var9) {
            JDBCLogger.getLogger().error("DecryptMapper.mapRow() exception , cause:" + var9.getMessage());
            return null;
        }
    }

    private boolean filterField (UserModel user, String fieldName) {
        if ("lt-order".equals(modelCode)) {
            return true;
        }
        List<JSONObject> fields = CryptorRightUtil.getField("2", user.getSchemaName());
        if (CollectionUtils.isEmpty(fields)) {
            return false;
        }
        List<String> cryptorField = fields.stream().map(field -> field.getString("CRYPTOR_FIELD")).collect(Collectors.toList());
        if (StringUtils.equalsAny(fieldName,cryptorField.toArray(new String[]{}))) {
            //logger.info("当前字段【"+fieldName+"】在限制范围内{"+exportField+"}");
            List<RoleModel> list = user.getRoles();
            if (CollectionUtils.isNotEmpty(list)) {
                return CryptorRightUtil.getRight("2", fieldName, user);
            }
            //logger.info("当前用户角色不在允许导出范围内{"+exportRole+"}");
        }
        return false;
    }


}
