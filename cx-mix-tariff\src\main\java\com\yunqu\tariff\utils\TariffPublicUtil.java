package com.yunqu.tariff.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.slf4j.Logger;

public class TariffPublicUtil {

    private static Logger logger = CommonLogger.getLogger();

    /**
     * 判断资费是否已公示
     * @param tariffNo 资费编号
     * @param auditDate 稽核日期
     * @return 是否已公示
     */
    public static boolean checkTariffIsPublic(String tariffNo, String auditDate) {
        try {
            if(StringUtils.isBlank(tariffNo)){
                return false;
            }

            if(StringUtils.isBlank(auditDate)){
                auditDate = EasyDate.getCurrentDateStr();
            }
            // 根据稽核日期确定应该使用的公示库版本
            String dateVersion = determinePublicDateId(auditDate);
            logger.info("稽核日期 {} 对应的公示库日期版本为: {}", auditDate, dateVersion);

            // 构建ES查询，从公示库中查询特定资费编号和日期的资费
            JSONObject queryParams = new JSONObject();
            queryParams.put("size", 10); // 只需要确认是否存在，获取少量数据即可

            // 构建查询条件
            JSONObject query = new JSONObject();
            JSONObject bool = new JSONObject();
            JSONArray must = new JSONArray();

            // 添加日期条件
            JSONObject dateTerms = new JSONObject();
            JSONObject dateFilter = new JSONObject();
            dateFilter.put("version_nos.keyword", new JSONArray().fluentAdd(dateVersion));
            dateTerms.put("terms", dateFilter);
            must.add(dateTerms);

            // 添加资费编号条件
            JSONObject tariffTerm = new JSONObject();
            JSONObject tariffFilter = new JSONObject();
            tariffFilter.put("tariff_no.keyword", tariffNo);
            tariffTerm.put("term", tariffFilter);
            must.add(tariffTerm);

            bool.put("must", must);
            query.put("bool", bool);
            queryParams.put("query", query);

            // 执行ES查询
            logger.info("查询公示库资费数据，参数: {}", queryParams.toJSONString());
            JSONObject esResult = ElasticsearchKit.search(Constants.XTY_TARIFF_PUBLIC_LIB_INDEX, queryParams);

            // 解析ES查询结果
            if (esResult != null && esResult.containsKey("hits")) {
                JSONObject hits = esResult.getJSONObject("hits");
                if (hits.containsKey("total") && hits.getJSONObject("total")!=null && hits.getJSONObject("total").getIntValue("value") > 0) {
                    // 存在匹配的资费记录
                    logger.info("资费 {} 在公示库中已存在", tariffNo);
                    return true;
                }
            }
            logger.info("资费 {} 在公示库中不存在", tariffNo);
            return false;
        } catch (Exception e) {
            logger.error("查询资费是否公示失败：reportNo:{}, 日期={}", tariffNo, auditDate, e);
            return false;
        }
    }


    /**
     * 根据稽核日期确定应该使用的公示库版本日期
     * 规则：1-10号使用当月1号版本，11-20号使用当月11号版本，21号及以后使用当月21号版本
     *
     * @param auditDate 稽核日期，格式为"yyyyMMdd"
     * @return 对应的公示库日期，格式为"yyyyMMdd"
     */
    public static String determinePublicDateId(String auditDate) {
        if (StringUtils.isBlank(auditDate)) {
            logger.error("稽核日期为空，无法确定公示库版本");
            return auditDate;
        }
        try {
            // 确保auditDate格式为yyyyMMdd
            if (auditDate.contains("-")) {
                auditDate = auditDate.replace("-", "");
            }

            EasySQL sql = new EasySQL("select version_no from " + Constants.getBusiSchema() + ".xty_crawler_version");
            sql.append("where 1=1");
            sql.append(auditDate, "and belong_date_id <= ?", false);
            sql.append("order by belong_date_id desc limit 1");
            return QueryFactory.getTariffQuery().queryForString(sql.getSQL(), sql.getParams());
        } catch (Exception e) {
            logger.error("解析稽核日期失败: {}", auditDate, e);
            return auditDate;
        }
    }

    /**
     * 判断资费是否已报送
     * @param tariffNo 资费编号
     * @return 是否已报送
     */
    public static boolean checkTariffIsReport(String tariffNo) {
        try {
            logger.debug("检查报送库是否存在 REPORT_NO = {}", tariffNo);

            // 构建ES查询参数
            JSONObject queryParams = new JSONObject();
            queryParams.put("size", 1); // 只查一条
            queryParams.put("track_total_hits", true);

            // 构建查询条件
            JSONObject boolQuery = new JSONObject();
            JSONArray mustArray = new JSONArray();

            // 添加 REPORT_NO 条件
            JSONObject termQuery = new JSONObject();
            JSONObject termFilter = new JSONObject();
            termFilter.put("REPORT_NO", tariffNo); // 使用 keyword 精确匹配
            termQuery.put("term", termFilter);
            mustArray.add(termQuery);

            // 组装最终的查询
            boolQuery.put("must", mustArray);
            queryParams.put("query", new JSONObject().fluentPut("bool", boolQuery));

            logger.debug("报送库检查参数: {}", queryParams.toJSONString());

            // 执行ES查询
            JSONObject esResult = ElasticsearchKit.search(Constants.XTY_TARIFF_BAK_INFO_INDEX, queryParams);

            if (esResult != null && esResult.containsKey("hits")) {
                JSONObject hits = esResult.getJSONObject("hits");
                JSONArray hitsArray = hits.getJSONArray("hits");
                boolean exists = hitsArray != null && !hitsArray.isEmpty();
                return exists;
            }

            return false;
        } catch (Exception e) {
            logger.error("检查 ES 中是否存在 REPORT_NO={} 的记录失败: {}", tariffNo, e.getMessage(), e);
            return false;
        }
    }


}
