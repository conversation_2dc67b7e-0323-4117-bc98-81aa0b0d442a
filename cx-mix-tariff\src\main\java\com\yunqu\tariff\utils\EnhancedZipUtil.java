package com.yunqu.tariff.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;

/**
 * 增强的ZIP处理工具类
 * 提供robust的ZIP文件解析功能，支持多种编码和异常处理
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
public class EnhancedZipUtil {

    private static final Logger logger = LoggerFactory.getLogger(EnhancedZipUtil.class);

    /**
     * 安全地解析ZIP文件中的文件列表
     *
     * @param zipFilePath ZIP文件路径
     * @return 文件列表，如果解析失败返回空列表
     */
    public static List<String> parseZipFileListSafely(String zipFilePath) {
        List<String> fileList = new ArrayList<>();

        try {
            // 首先验证ZIP文件
            ZipFileValidator.ValidationResult validation = ZipFileValidator.validateZipFile(zipFilePath);
            if (!validation.isValid()) {
                logger.error("ZIP文件验证失败: {}", validation.getErrorMessage());
                return fileList;
            }

            // 使用验证推荐的编码解析文件
            Charset charset = validation.getSuggestedCharset();
            fileList = parseZipFileListWithCharset(zipFilePath, charset);

            if (fileList.isEmpty()) {
                logger.warn("使用推荐编码 {} 解析ZIP文件为空，尝试其他方法", charset.name());
                fileList = parseZipFileListFallback(zipFilePath);
            }

            logger.info("成功解析ZIP文件，包含 {} 个文件", fileList.size());

        } catch (Exception e) {
            logger.error("解析ZIP文件失败: " + zipFilePath, e);
        }

        return fileList;
    }

    /**
     * 使用指定编码解析ZIP文件列表
     */
    private static List<String> parseZipFileListWithCharset(String zipFilePath, Charset charset) {
        List<String> fileList = new ArrayList<>();

        try (ZipFile zipFile = new ZipFile(zipFilePath, charset)) {
            java.util.Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                if (!entry.isDirectory()) {
                    String fileName = extractFileName(entry.getName());
                    if (isValidFileName(fileName)) {
                        fileList.add(fileName);
                    }
                }
            }
        } catch (Exception e) {
            logger.debug("使用编码 {} 解析ZIP文件失败: {}", charset.name(), e.getMessage());
            throw new RuntimeException("ZIP文件解析失败", e);
        }

        return fileList;
    }

    /**
     * 备用解析方法，使用ZipInputStream
     */
    private static List<String> parseZipFileListFallback(String zipFilePath) {
        List<String> fileList = new ArrayList<>();
        
        // 尝试多种编码
        Charset[] charsets = {
            StandardCharsets.UTF_8,
            Charset.forName("GBK"),
            Charset.forName("GB2312"),
            StandardCharsets.ISO_8859_1
        };

        for (Charset charset : charsets) {
            try {
                fileList = parseWithZipInputStream(zipFilePath, charset);
                if (!fileList.isEmpty()) {
                    logger.info("使用编码 {} 通过ZipInputStream成功解析ZIP文件", charset.name());
                    break;
                }
            } catch (Exception e) {
                logger.debug("使用编码 {} 的ZipInputStream解析失败: {}", charset.name(), e.getMessage());
            }
        }

        return fileList;
    }

    /**
     * 使用ZipInputStream解析
     */
    private static List<String> parseWithZipInputStream(String zipFilePath, Charset charset) throws IOException {
        List<String> fileList = new ArrayList<>();

        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFilePath), charset)) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    String fileName = extractFileName(entry.getName());
                    if (isValidFileName(fileName)) {
                        fileList.add(fileName);
                    }
                }
                zis.closeEntry();
            }
        }

        return fileList;
    }

    /**
     * 安全地从ZIP文件中读取指定文件的输入流
     *
     * @param zipFilePath ZIP文件路径
     * @param targetFileName 目标文件名
     * @return 输入流，如果文件不存在或读取失败返回null
     */
    public static InputStream getFileInputStreamSafely(String zipFilePath, String targetFileName) {
        try {
            // 验证ZIP文件
            ZipFileValidator.ValidationResult validation = ZipFileValidator.validateZipFile(zipFilePath);
            if (!validation.isValid()) {
                logger.error("ZIP文件验证失败: {}", validation.getErrorMessage());
                return null;
            }

            Charset charset = validation.getSuggestedCharset();
            return getFileInputStream(zipFilePath, targetFileName, charset);

        } catch (Exception e) {
            logger.error("从ZIP文件读取文件失败: zipFile={}, targetFile={}", zipFilePath, targetFileName, e);
            return null;
        }
    }

    /**
     * 使用指定编码从ZIP文件中获取文件输入流
     */
    private static InputStream getFileInputStream(String zipFilePath, String targetFileName, Charset charset) throws IOException {
        ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFilePath), charset);
        
        try {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    String fileName = extractFileName(entry.getName());
                    if (fileName.equals(targetFileName)) {
                        // 找到目标文件，返回当前的ZipInputStream
                        // 注意：调用者负责关闭这个流
                        return zis;
                    }
                }
                zis.closeEntry();
            }
        } catch (Exception e) {
            // 如果出错，确保关闭流
            try {
                zis.close();
            } catch (IOException closeException) {
                logger.warn("关闭ZipInputStream失败", closeException);
            }
            throw e;
        }

        // 没有找到目标文件，关闭流
        zis.close();
        return null;
    }

    /**
     * 从完整路径中提取文件名
     */
    private static String extractFileName(String entryName) {
        if (entryName == null || entryName.trim().isEmpty()) {
            return "";
        }

        // 处理路径分隔符
        String fileName = entryName;
        if (fileName.contains("/")) {
            fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
        }
        if (fileName.contains("\\")) {
            fileName = fileName.substring(fileName.lastIndexOf("\\") + 1);
        }

        return fileName.trim();
    }

    /**
     * 验证文件名是否有效
     */
    private static boolean isValidFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }

        // 过滤系统文件和隐藏文件
        if (fileName.startsWith(".") || fileName.startsWith("__MACOSX")) {
            return false;
        }

        // 过滤临时文件
        if (fileName.startsWith("~") || fileName.endsWith(".tmp")) {
            return false;
        }

        return true;
    }

    /**
     * 创建临时文件从ZIP输入流
     *
     * @param inputStream ZIP输入流
     * @param originalFileName 原始文件名
     * @return 临时文件
     * @throws IOException IO异常
     */
    public static File createTempFileFromStream(InputStream inputStream, String originalFileName) throws IOException {
        if (inputStream == null || originalFileName == null) {
            throw new IllegalArgumentException("输入流和文件名不能为空");
        }

        String extension = "";
        int dotIndex = originalFileName.lastIndexOf(".");
        if (dotIndex > 0 && dotIndex < originalFileName.length() - 1) {
            extension = originalFileName.substring(dotIndex);
        }

        File tempFile = File.createTempFile("zip_extract_", extension);
        
        try (FileOutputStream fos = new FileOutputStream(tempFile);
             BufferedOutputStream bos = new BufferedOutputStream(fos)) {
            
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
            bos.flush();
        }

        return tempFile;
    }

    /**
     * 安全地关闭输入流
     */
    public static void closeStreamSafely(InputStream inputStream) {
        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
                logger.warn("关闭输入流失败", e);
            }
        }
    }
}