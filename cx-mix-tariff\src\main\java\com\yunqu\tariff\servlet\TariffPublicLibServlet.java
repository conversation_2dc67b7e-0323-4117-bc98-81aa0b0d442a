package com.yunqu.tariff.servlet;

import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.UserUtil;
import com.yq.busi.common.util.mq.MQBrokerUtil;
import com.yunqu.tariff.base.AppBaseServlet;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.service.TariffPublicService;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.slf4j.Logger;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Enumeration;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <p>
 * 公示库管理Servlet
 * </p>
 *
 * @ClassName TariffPublicLibServlet
 * @Description 公示库管理相关接口
 * @Since create in 2024
 * @Version v1.0
 * @Company 广州云趣信息科技有限公司
 */
@WebServlet("/servlet/tariffPublicLib/*")
public class TariffPublicLibServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;

    private Logger logger = CommonLogger.getLogger();

    private static final ExecutorService executorService = Executors.newSingleThreadExecutor();

//    /**
//     * 创建公示库ES索引
//     * 只创建索引结构，不同步数据
//     */
//    public JSONObject actionForCreateEsIndex() {
//        try {
//            TariffCrawlCheckService service = new TariffCrawlCheckService();
//            service.createEsIndex();
//            return EasyResult.ok("创建索引成功");
//        } catch (Exception e) {
//            logger.error("创建索引失败: {}", e.getMessage(), e);
//            return EasyResult.fail("创建索引失败: " + e.getMessage());
//        }
//    }


    public JSONObject actionForExportCrawl() {
        try {
            JSONObject param = getJSONObject();
            UserModel user = UserUtil.getUser(getRequest());
            String serialId = IdUtil.getSnowflakeNextIdStr();

            JSONObject json = new JSONObject();
            json.put("ID", serialId);
            json.put("TASK_CODE", "TariffCrawlExportHandler");
            json.put("TASK_NAME", "公示库资费列表导出");
            json.put("CREATOR", user.getUserName());
            json.put("CREATE_ACCT", user.getUserAcc());
            json.put("CREATE_TIME", EasyDate.getCurrentTimeStampString());
            json.put("PARAMS", param.toString());
            json.put("STATUS", 1);
            json.put("FILE_NAME", "公示库资费列表_" + EasyDate.getCurrentDateString("yyyyMMdd") + "_" + user.getUserAcc() + "_" + serialId + ".xlsx");

            EasyRecord record = new EasyRecord(getTableName("xty_tariff_export_task"), "ID");
            record.setColumns(json);
            getQuery().save(record);
            json.put("PARAMS", param);
            JSONObject params = new JSONObject();
            params.put("serialId", serialId);
            params.put("command", "TariffCrawlExportHandler");
            params.put("taskObj", json);
            MQBrokerUtil.sendMsg(Constants.TARIFF_NOTIFY_EXPORT_BROKER, params.toString());

            return EasyResult.ok("", "创建导出任务成功");
        } catch (Exception e) {
            error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    /**
     * 根据公示库ID查询对应的所有爬虫记录数据
     *
     * @return 查询结果
     * @throws Exception 异常信息
     */
    public JSONObject actionForGetCrawlRecordsByPublicLibId() throws Exception {
        try {
            // 获取公示库ID参数
            String publicLibId = this.getJSONObject().getString("publicLibId");

            if (StringUtils.isBlank(publicLibId)) {
                return EasyResult.fail("公示库ID不能为空");
            }

            logger.info("根据公示库ID查询爬虫记录，publicLibId={}", publicLibId);

            // 需要获取的记录数量
            final int LIMIT_COUNT = 12;

            // 存储查询结果
            List<JSONObject> resultRecords = new ArrayList<>();

            // 首先查询所有版本表信息
            EasySQL versionSql = new EasySQL();
            versionSql.append("SELECT VERSION_NO, VERSION_TABLE_NAME FROM " + Constants.getBusiSchema() + ".xty_crawler_version");
            versionSql.append("ORDER BY UPDATE_TIME DESC");

            logger.info(versionSql.toFullSql());
            List<JSONObject> versionTables = QueryFactory.getTariffQuery().queryForList(versionSql.getSQL(), versionSql.getParams(), new JSONMapperImpl());

            // 添加默认表
            JSONObject defaultTable = new JSONObject();
            defaultTable.put("VERSION_NO", "default");
            defaultTable.put("VERSION_TABLE_NAME", "xty_tariff_crawl_record");
            versionTables.add(defaultTable);

            logger.info("查询到{}张爬虫记录表", versionTables.size());

            // 依次查询每个版本表
            for (JSONObject versionTable : versionTables) {
                String tableName = versionTable.getString("VERSION_TABLE_NAME");
                String versionNo = versionTable.getString("VERSION_NO");

                if (StringUtils.isBlank(tableName)) {
                    logger.warn("版本{}的表名为空", versionNo);
                    tableName = "xty_tariff_crawl_record";
                }

                // 构建查询SQL
                EasySQL sql = new EasySQL();
                sql.append("SELECT * FROM " + Constants.getBusiSchema() + "." + tableName + " WHERE 1=1");
                sql.append(publicLibId, "AND PUBLIC_LIB_ID = ?");
                sql.append("ORDER BY UPDATE_TIME DESC");

                // 执行查询
                List<JSONObject> records = QueryFactory.getTariffQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

                if (records != null && !records.isEmpty()) {
                    logger.info("在版本表{}中查询到{}条爬虫记录", tableName, records.size());

                    // 添加版本信息到每条记录
                    for (JSONObject record : records) {
                        record.put("VERSION_TABLE_NAME", tableName);
                        record.put("VERSION_NO", versionNo);
                    }

                    // 添加到结果集
                    resultRecords.addAll(records);

                    // 如果已经达到或超过需要的记录数，则停止查询
                    if (resultRecords.size() >= LIMIT_COUNT) {
                        logger.info("已达到需要的记录数量{}", LIMIT_COUNT);
                        break;
                    }
                } else {
                    logger.info("在版本表{}中未查询到爬虫记录", tableName);
                }
            }

            // 如果结果超过限制数量，截取前LIMIT_COUNT条
            if (resultRecords.size() > LIMIT_COUNT) {
                resultRecords = resultRecords.subList(0, LIMIT_COUNT);
            }

            logger.info("最终查询到{}条爬虫记录数据", resultRecords.size());

            return EasyResult.ok(resultRecords);
        } catch (Exception e) {
            logger.error("根据公示库ID查询历史记录失败", e);
            return EasyResult.fail("查询失败：" + e.getMessage());
        }
    }

    public JSONObject actionForCheckPublicLibReportRelation() {
        try {
            // 获取公示库ID数组参数
            JSONObject param = getJSONObject();
            List<String> publicLibIds = param.getJSONArray("publicLibIds").toJavaList(String.class);

            if (publicLibIds == null || publicLibIds.isEmpty()) {
                return EasyResult.fail("公示库ID数组不能为空");
            }

            logger.info("检查公示库记录是否关联报送库记录，publicLibIds={}", publicLibIds);

            // 构建结果集
            JSONObject result = new JSONObject();
            result.put("total", publicLibIds.size());

            // 存储关联成功的记录
            List<JSONObject> relatedRecords = new ArrayList<>();
            // 存储未关联的记录ID
            List<String> unrelatedIds = new ArrayList<>();

            // 查询每个公示库记录
            for (String publicLibId : publicLibIds) {
                // 构建查询SQL - 通过方案编号关联
//                EasySQL sql = new EasySQL();
////                sql.append("SELECT a.ID, a.NAME, a.TARIFF_NO, a.TARIFF_RECORD_ID, a.REPORTED, ");
////                sql.append("b.ID as REPORT_ID, b.NAME as REPORT_NAME, b.REPORT_NO, b.TYPE1, b.TYPE2, b.IS_TELECOM, ");
////                sql.append("b.VERSION_NUM, b.REPORTER ");
////                sql.append("FROM " + Constants.getBusiSchema() + ".xty_tariff_crawl_record_lib a ");
////                sql.append("LEFT JOIN " + Constants.getBusiSchema() + ".xty_tariff_record b ON a.TARIFF_NO = b.REPORT_NO ");
////                sql.append(publicLibId,"WHERE a.ID = ?");

                // 1. 先查询公示库记录
                EasySQL publicLibSql = new EasySQL();
                publicLibSql.append("SELECT ID, NAME, TARIFF_NO, TARIFF_RECORD_ID, REPORTED ");
                publicLibSql.append("FROM " + Constants.getBusiSchema() + ".xty_tariff_crawl_record_lib ");
                publicLibSql.append(publicLibId, "WHERE ID = ?");

                // 执行查询
                JSONObject publicLibRecord = QueryFactory.getTariffQuery().queryForRow(publicLibSql.getSQL(), publicLibSql.getParams(), new JSONMapperImpl());

                String tariffNo = publicLibRecord.getString("TARIFF_NO");
                if (StringUtils.isBlank(tariffNo)) {
                    return EasyResult.fail("比对失败：这条公示记录没有方案编号");
                }

                // 2. 根据方案编号查询报送库记录
                EasySQL reportSql = new EasySQL();
                reportSql.append("SELECT ID, NAME, REPORT_NO, TYPE1, TYPE2, IS_TELECOM, VERSION_NUM, REPORTER ");
                reportSql.append("FROM " + Constants.getBusiSchema() + ".xty_tariff_record ");
                reportSql.append(tariffNo, "WHERE REPORT_NO = ?");

                JSONObject reportRecord = QueryFactory.getWriteQuery().queryForRow(reportSql.getSQL(), reportSql.getParams(), new JSONMapperImpl());


                if (reportRecord != null) {
                    // 判断是否关联报送库 - 检查是否有报送库的记录ID
                    boolean isRelated = StringUtils.isNotBlank(reportRecord.getString("ID"));

                    if (isRelated) {
                        // 构建关联成功的记录信息
                        JSONObject relatedInfo = new JSONObject();
                        relatedInfo.put("publicLibId", publicLibRecord.getString("ID"));
                        relatedInfo.put("publicLibName", publicLibRecord.getString("NAME"));
                        relatedInfo.put("tariffNo", publicLibRecord.getString("TARIFF_NO"));
                        relatedInfo.put("reportRecordId", reportRecord.getString("ID"));
                        relatedInfo.put("reportName", reportRecord.getString("NAME"));
                        relatedInfo.put("reportNo", reportRecord.getString("REPORT_NO"));
                        relatedInfo.put("type1", reportRecord.getString("TYPE1"));
                        relatedInfo.put("type2", reportRecord.getString("TYPE2"));
                        relatedInfo.put("isTelecom", reportRecord.getString("IS_TELECOM"));

                        // 添加ES索引更新状态
                        relatedInfo.put("esUpdateStatus", "未执行");

                        // 获取当前月份，格式为yyyyMM
                        String currentMonth = new SimpleDateFormat("yyyyMM").format(new Date());
                        String reportNo = reportRecord.getString("REPORT_NO");
                        String reportRecordId = reportRecord.getString("ID");
                        String versionNum = reportRecord.getString("VERSION_NUM");
                        String tariffName = reportRecord.getString("REPORT_NAME");
                        String reporter = reportRecord.getString("REPORTER");

                        try {
                            // 1. 更新报送库的是否公示字段
                            EasySQL updateSql = new EasySQL();
                            updateSql.append(reportRecordId, "UPDATE " + Constants.getBusiSchema() + ".xty_tariff_record SET IS_PUBLIC = 'Y' WHERE ID = ?");
                            int updateResult = QueryFactory.getWriteQuery().executeUpdate(updateSql.getSQL(), updateSql.getParams());

                            relatedInfo.put("dbUpdateStatus", updateResult > 0 ? "成功" : "失败");

                            // 2. 如果数据库更新成功，计算 serialId 并更新ES索引
                            if (updateResult > 0) {
                                try {
                                    // 计算 serialId，使用与 getTariffSerialId 相同的逻辑
                                    String serialId = SecureUtil.sha256(tariffName + reportNo + "1" + reporter);

                                    // 更新公示库记录的报送关联ID
                                    EasySQL updateLibSql = new EasySQL();
                                    updateLibSql.append(reportRecordId, "UPDATE " + Constants.getBusiSchema() + ".xty_tariff_crawl_record_lib SET TARIFF_RECORD_ID = ?");
                                    updateLibSql.append(publicLibId, ", REPORTED = '1' WHERE ID = ?");
                                    int libUpdateResult = QueryFactory.getTariffQuery().executeUpdate(updateLibSql.getSQL(), updateLibSql.getParams());

                                    logger.info("更新公示库关联ID结果: {}", libUpdateResult > 0 ? "成功" : "失败");

                                    // 使用 TariffPublicService 实例更新ES索引
                                    TariffPublicService.getInstance().updateBakEsIsPublic(reportNo, reportRecordId, "Y", versionNum, currentMonth);

                                    // 更新存储索引中的公示状态
                                    TariffPublicService.getInstance().updateStorageEsIsPublic(reportNo, serialId, "Y", versionNum, currentMonth);

                                    relatedInfo.put("esUpdateStatus", "成功");
                                    relatedInfo.put("serialId", serialId);
                                    logger.info("更新ES索引公示状态成功，reportNo: {}, recordId: {}, serialId: {}",
                                            reportNo, reportRecordId, serialId);
                                } catch (Exception e) {
                                    relatedInfo.put("esUpdateStatus", "失败: " + e.getMessage());
                                    logger.error("更新ES索引公示状态失败: {}", e.getMessage(), e);
                                }
                            }
                        } catch (Exception e) {
                            relatedInfo.put("dbUpdateStatus", "失败: " + e.getMessage());
                            logger.error("更新数据库公示状态失败: {}", e.getMessage(), e);
                        }

                        relatedRecords.add(relatedInfo);
                    } else {
                        unrelatedIds.add(publicLibId);
                    }
                } else {
                    // 记录不存在，添加到未关联列表
                    unrelatedIds.add(publicLibId);
                }
            }

            result.put("relatedCount", relatedRecords.size());
            result.put("unrelatedCount", unrelatedIds.size());
            result.put("relatedRecords", relatedRecords);
            result.put("unrelatedIds", unrelatedIds);

            logger.info("检查完成，共{}条记录，比对报送库成功有{}条，已更新数据库和ES索引",
                    publicLibIds.size(), relatedRecords.size());

            return EasyResult.ok(result);
        } catch (Exception e) {
            logger.error("检查公示库记录关联关系失败", e);
            return EasyResult.fail("检查失败：" + e.getMessage());
        }
    }

    /**
     * 省内公示资费数量变化统计导出
     *
     * @return
     */
    public JSONObject actionForProvPublicChgStat() {
        try {
            JSONObject param = requestToJsonObject();
            logger.info("开始执行省内公示资费数量变化统计导出，参数param => " + JSON.toJSONString(param));
            UserModel user = UserUtil.getUser(getRequest());
            String serialId = IdUtil.getSnowflakeNextIdStr();

            JSONObject json = new JSONObject();
            json.put("ID", serialId);
            json.put("TASK_CODE", "provTariffChgStat");
            json.put("TASK_NAME", "省内公示资费数量变化统计");
            json.put("CREATOR", user.getUserName());
            json.put("CREATE_ACCT", user.getUserAcc());
            json.put("CREATE_TIME", EasyDate.getCurrentTimeStampString());
            json.put("PARAMS", param.toString());
            json.put("STATUS", 1);
            json.put("FILE_NAME", "省内公示资费数量变化统计_" + EasyDate.getCurrentDateString("yyyyMMdd") + "_" + user.getUserAcc() + "_" + serialId + ".xlsx");

            EasyRecord record = new EasyRecord(getTableName("xty_tariff_export_task"), "ID");
            record.setColumns(json);
            getQuery().save(record);
            json.put("PARAMS", param);
            JSONObject params = new JSONObject();
            params.put("serialId", serialId);
            params.put("command", "provTariffChgStat");
            params.put("taskObj", json);
            MQBrokerUtil.sendMsg(Constants.TARIFF_NOTIFY_EXPORT_BROKER, params.toString());
            return EasyResult.ok("", "创建导出任务成功");
        } catch (Exception e) {
            error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    /**
     * 公示资费报送内容情况统计
     *
     * @return
     */
    public JSONObject actionForTariffReportChkStat() {
        try {
            JSONObject param = requestToJsonObject();
            UserModel user = UserUtil.getUser(getRequest());
            String serialId = IdUtil.getSnowflakeNextIdStr();
            param.put("entId", user.getEpCode());
            param.put("busiOrderId", user.getBusiOrderId());

            JSONObject json = new JSONObject();
            json.put("ID", serialId);
            json.put("TASK_CODE", "tariffReportChkStat");
            json.put("TASK_NAME", "公示资费报送内容情况统计");
            json.put("CREATOR", user.getUserName());
            json.put("CREATE_ACCT", user.getUserAcc());
            json.put("CREATE_TIME", EasyDate.getCurrentTimeStampString());
            json.put("PARAMS", param.toString());
            json.put("STATUS", 1);
            json.put("FILE_NAME", "公示资费报送内容情况统计_" + EasyDate.getCurrentDateString("yyyyMMdd") + "_" + user.getUserAcc() + "_" + serialId + ".xlsx");

            EasyRecord record = new EasyRecord(getTableName("xty_tariff_export_task"), "ID");
            record.setColumns(json);
            getQuery().save(record);
            json.put("PARAMS", param);
            JSONObject params = new JSONObject();
            params.put("serialId", serialId);
            params.put("command", "tariffReportChkStat");
            params.put("taskObj", json);
            MQBrokerUtil.sendMsg(Constants.TARIFF_NOTIFY_EXPORT_BROKER, params.toString());
            return EasyResult.ok("", "创建导出任务成功");
        } catch (Exception e) {
            error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    private JSONObject requestToJsonObject() {
        HttpServletRequest request = getRequest();
        Enumeration<String> paramNames = request.getParameterNames();
        List<String> paramList = JSON.parseObject(JSON.toJSONString(paramNames), new TypeReference<List<String>>() {
        });
        JSONObject requestJson = new JSONObject();
        for (String paramName : paramList) {
            String[] pv = request.getParameterValues(paramName);
            if (pv == null || pv.length == 0) {
                continue;
            }
            // 处理数组参数
            if (StringUtils.contains(paramName, "[]")) {
                String cleanParamName = paramName.replace("[]", "");
                // 构建 JSONArray
                JSONArray array = new JSONArray();
                for (String value : pv) {
                    if (value != null && !value.isEmpty()) {
                        array.add(value);
                    }
                }
                // 放入 JSON 对象
                requestJson.put(cleanParamName, array);
            } else { // 处理字符串参数
                requestJson.put(paramName, pv[0]);
            }
        }
        return requestJson;
    }
}