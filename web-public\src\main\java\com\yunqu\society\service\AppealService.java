package com.yunqu.society.service;

import java.io.IOException;
import java.sql.SQLException;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.society.base.BaseService;
import com.yunqu.society.base.CommonLogger;
import com.yunqu.society.base.Constants;
import com.yunqu.society.base.QueryFactory;
import com.yunqu.society.base.mapper.HumpMapper;
import com.yunqu.society.model.BoxAppeal;
import com.yunqu.society.util.OkHttpUtil;
import com.yunqu.society.util.PhoneCryptor;
import com.yunqu.society.util.RedisLockUtil;
import com.yunqu.society.util.Sm4Util;

public class AppealService extends BaseService{

    private static Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("appeal").getName());

    private static final String INSERT_KEY = "appeal_insert:";
    
    private static final String APPEAL_ID_SEQUENCE_KEY = "appeal_id_sequence:";

    public static final String DUPLICATE_STATUS = "100";

    public static final String SUCCESS_STATUS = "200";

    public static final String INSERT_STATUS = "300";
    
    // 申诉来源标识（已迁移到Constants类）
    @Deprecated
    public static final String SOURCE_WECHAT_MINI = Constants.SOURCE_WECHAT_MINI; // 微信小程序
    @Deprecated
    public static final String SOURCE_WECHAT_PUBLIC = Constants.SOURCE_WECHAT_PUBLIC; // 微信公众号
    @Deprecated
    public static final String SOURCE_WEBSITE = Constants.SOURCE_WEBSITE; // 网站
    

    
    /**
     * 构建申诉ID
     * 格式：YYYYMMDD + 2位标识（01微信小程序,02微信公众号，03网站，）+ 6位序列号
     * 
     * @param sourceType 申诉来源类型（01微信小程序,02微信公众号，03网站）
     * @return 申诉ID
     */
    public String generateAppealId(String sourceType) {
        // 获取当前日期（YYYYMMDD格式）
        String dateStr = DateUtil.getCurrentDateStr("yyyyMMdd");
        
        // 构建缓存key
        String cacheKey = APPEAL_ID_SEQUENCE_KEY + dateStr + "_";
        
        // 使用Redis分布式锁确保并发安全
        return RedisLockUtil.executeLocked(cacheKey, 10, 3000, () -> {
            try {
                // 1. 先从缓存获取当前序列号
                Integer currentSequence = CacheUtil.get(cacheKey);
                
                if (currentSequence == null) {
                    // 2. 缓存为空，从数据库获取当天该来源类型的最大序列号
                    currentSequence = getMaxSequenceFromDatabase();
                }
                
                // 3. 序列号加1
                currentSequence++;
                
                // 4. 更新缓存（设置过期时间为当天结束）
                long expireSeconds = getSecondsUntilEndOfDay();
                CacheUtil.put(cacheKey, currentSequence, (int) expireSeconds);
                
                // 5. 构建完整的申诉ID
                String appealId = String.format("%s%s%06d", dateStr, sourceType, currentSequence);
                
                logger.info("[生成申诉ID] 日期: {}, 来源: {}, 序列号: {}, 申诉ID: {}", 
                           dateStr, sourceType, currentSequence, appealId);
                
                return "部-" + appealId;
                
            } catch (Exception e) {
                logger.error("[生成申诉ID异常] 日期: {}, 来源: {}, 错误: {}", dateStr, sourceType, e.getMessage(), e);
                throw new RuntimeException("生成申诉ID失败: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * 从数据库获取指定日期和来源类型的最大序列号
     * 
     * @param dateStr 日期字符串（YYYYMMDD）
     * @param sourceType 来源类型
     * @return 最大序列号，如果没有记录则返回0
     */
    private Integer getMaxSequenceFromDatabase() {
        try {
            EasySQL sql = new EasySQL();
            sql.append("SELECT max(substr(SERIAL_ID,6)) as max_sequence ");
            sql.append("FROM " + getTableName("c_box_appeal") + " ");
            sql.append("WHERE 1=1");
            sql.append(DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD) + " 00:00:00"," AND CREATE_TIME >= ? ");
            sql.append(DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD) + " 23:59:59"," AND CREATE_TIME <= ? ");
            
            logger.info("[查询最大序列号] SQL: {}", sql.getFullSq());
            
            JSONObject record = QueryFactory.getReadQuery().queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
            
            Integer maxSequence = 0;
            if (record != null && record.get("max_sequence") != null) {
                maxSequence = record.getIntValue("max_sequence");
            }
            
            logger.info("[查询最大序列号] , 最大序列号: {}",  maxSequence);
            
            return maxSequence;
            
        } catch (SQLException e) {
            logger.error("[查询最大序列号异常] 错误: {}", e.getMessage(), e);
            throw new RuntimeException("查询最大序列号失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取到当天结束的秒数
     * 
     * @return 秒数
     */
    private long getSecondsUntilEndOfDay() {
        try {
            java.time.LocalDateTime now = java.time.LocalDateTime.now();
            java.time.LocalDateTime endOfDay = now.toLocalDate().atTime(23, 59, 59);
            return java.time.Duration.between(now, endOfDay).getSeconds() + 1;
        } catch (Exception e) {
            // 如果计算失败，默认设置24小时过期
            return 24 * 60 * 60;
        }
    }
    
    /**
     * 检查是否重复提交申诉
     * @param md5 申诉信息的MD5值
     * @param result 结果对象
     * @return 是否重复提交
     */
    public boolean isRepeatSubmit(String md5, JSONObject result) {
        // 检查缓存中是否存在该md5
        String val = (String)CacheUtil.get(md5);
        if (StringUtils.isNotBlank(val) && "1".equals(result)) {
            result.putAll(createResult(DUPLICATE_STATUS, "请勿重复提交申诉信息"));;
            return true;
        }
        return false;
    }

    /**
     * 保存申诉信息
     * @param appeal
     */
    public void saveAppeal(BoxAppeal appeal,String md5) {
        // 保存申诉信息
        AppealQueueService.getInstance().add(appeal);
        // 将md5存入缓存,设置5分钟过期时间
        CacheUtil.put(md5, "1", 5 * 60);
    }

    /**
     * 申诉数据入库
     * @param appeal
     * @return
     */
    public JSONObject addAppeal(BoxAppeal appeal) {
        JSONObject result = createResult(SUCCESS_STATUS, "添加申诉成功");
    	String id = appeal.getId();
        return RedisLockUtil.executeLocked(id, 5, 1 * 1000,()-> {
            if ( CacheUtil.get(INSERT_KEY + id) != null) {
                return createResult(DUPLICATE_STATUS, "请勿重复提交申诉信息");
            }
            EasyRecord record = new EasyRecord( getTableName("c_box_appeal"),"ID");
            // 根据表结构构建record对象
            record.set("ID", id);
            record.set("M_ID", appeal.getMId());
            record.set("ENT_ID", appeal.getEntId());
            record.set("BUSI_ORDER_ID", appeal.getBusiOrderId());
            record.set("CREATE_TIME", DateUtil.getCurrentDateStr());
            record.set("ENT_TYPE", appeal.getEntType());
            record.set("ENT_DEPT_CODE", appeal.getEntDeptCode());
            record.set("ENT_DEPT_NAME", PhoneCryptor.getInstance().encrypt(appeal.getEntDeptName()));
            record.set("PROVINCE_CODE", appeal.getProvinceCode());
            record.set("PROVINCE_NAME", appeal.getProvinceName());
            record.set("CITY_CODE", appeal.getCityCode());
            record.set("CITY_NAME", appeal.getCityName());
            record.set("IS_COMPLAINED", appeal.getIsComplained());
            record.set("IS_BACK_RESULT", appeal.getIsBackResult());
            record.set("IS_SATISFY_RESULT", appeal.getIsSatisfyResult());
            record.set("APPEAL_TIME", appeal.getAppealTime());
            record.set("APPEAL_NAME", PhoneCryptor.getInstance().encrypt(appeal.getAppealName()));
            record.set("CARD_TYPE", appeal.getCardType());
            record.set("ID_CARD", PhoneCryptor.getInstance().encrypt(appeal.getIdCard()));
            record.set("PHONE", PhoneCryptor.getInstance().encrypt(appeal.getPhone()));
            record.set("CONCACT_NAME", PhoneCryptor.getInstance().encrypt(appeal.getConcactName()));
            record.set("CONCACT_CARD_TYPE", appeal.getConcactCardType());
            record.set("CONCACT_ID_CARD", PhoneCryptor.getInstance().encrypt(appeal.getConcactIdCard()));
            record.set("APPEAL_PHONE", PhoneCryptor.getInstance().encrypt(appeal.getAppealPhone()));
            record.set("APPEAL_CONTENT", appeal.getAppealContent());
            record.set("APPEAL_ATTACHMENT", appeal.getAppealAttachment());
            record.set("APPEAL_CODE", appeal.getAppealCode());
            record.set("EMAIL", PhoneCryptor.getInstance().encrypt(appeal.getEmail()));
            record.set("ADDRESS", PhoneCryptor.getInstance().encrypt(appeal.getAddress()));
            record.set("APPEAL_SOURCE", appeal.getAppealSource());
            record.set("POST_CODE", appeal.getPostCode());
            record.set("COMPLAIN_TIME", appeal.getComplainTime());
            record.set("IP_ADDRESS", appeal.getIpAddress());
            record.set("IS_SYSN", "N");
            record.set("SERVICE_STATUS", appeal.getServiceStatus());
            record.set("IS_APPEAL", appeal.getIsAppeal());
            record.set("ORG_CODE",appeal.getOrgCode());
            record.set("ORG_NAME", appeal.getOrgName());
            record.set("EXIST_ATTACHMENT",appeal.getExistAttachment());
            if (StringUtils.isBlank(appeal.getSerialId())) {
                appeal.setSerialId(generateAppealId(appeal.getAppealSource()));
            }
            record.set("SERIAL_ID", appeal.getSerialId());
            try {
                logger.info("[添加申诉请求参数]：" + JSONObject.toJSONString(record));
                QueryFactory.getWriteQuery().save(record);
            } catch (SQLException e) {
                String message = e.getMessage();
                logger.error(message, e);
                if (message.contains("Violate unique")) {
                    logger.info("工单已经入库，本次不做处理");
                    //return createResult(DUPLICATE_STATUS, "添加申诉失败");
                }
                return createResult(INSERT_STATUS, "添加申诉失败");
            }
            CacheUtil.put(INSERT_KEY + id, "1", 24 * 60 * 60);
            return result;
        });
    }


    public JSONObject updateAppeal(BoxAppeal appeal) {
        JSONObject result = createResult(SUCCESS_STATUS, "更新申诉成功");
    	String id = appeal.getId();
        return RedisLockUtil.executeLocked(id, 5, 1 * 1000,()-> {
            
            EasyRecord record = new EasyRecord( getTableName("c_box_appeal"),"ID");
            // 根据表结构构建record对象
            record.set("ID", id);
            record.set("M_ID", appeal.getMId());
            record.set("ENT_ID", appeal.getEntId());
            record.set("BUSI_ORDER_ID", appeal.getBusiOrderId());
            record.set("CREATE_TIME", DateUtil.getCurrentDateStr());
            record.set("ENT_TYPE", appeal.getEntType());
            record.set("ENT_DEPT_CODE", appeal.getEntDeptCode());
            record.set("ENT_DEPT_NAME", PhoneCryptor.getInstance().encrypt(appeal.getEntDeptName()));
            record.set("PROVINCE_CODE", appeal.getProvinceCode());
            record.set("PROVINCE_NAME", appeal.getProvinceName());
            record.set("CITY_CODE", appeal.getCityCode());
            record.set("CITY_NAME", appeal.getCityName());
            record.set("IS_COMPLAINED", appeal.getIsComplained());
            record.set("IS_BACK_RESULT", appeal.getIsBackResult());
            record.set("IS_SATISFY_RESULT", appeal.getIsSatisfyResult());
            record.set("APPEAL_TIME", appeal.getAppealTime());
            record.set("APPEAL_NAME", PhoneCryptor.getInstance().encrypt(appeal.getAppealName()));
            record.set("CARD_TYPE", appeal.getCardType());
            record.set("ID_CARD", PhoneCryptor.getInstance().encrypt(appeal.getIdCard()));
            record.set("PHONE", PhoneCryptor.getInstance().encrypt(appeal.getPhone()));
            record.set("CONCACT_NAME", PhoneCryptor.getInstance().encrypt(appeal.getConcactName()));
            record.set("CONCACT_CARD_TYPE", appeal.getConcactCardType());
            record.set("CONCACT_ID_CARD", PhoneCryptor.getInstance().encrypt(appeal.getConcactIdCard()));
            record.set("APPEAL_PHONE", PhoneCryptor.getInstance().encrypt(appeal.getAppealPhone()));
            record.set("APPEAL_CONTENT", appeal.getAppealContent());
            record.set("APPEAL_ATTACHMENT", appeal.getAppealAttachment());
            record.set("APPEAL_CODE", appeal.getAppealCode());
            record.set("EMAIL", PhoneCryptor.getInstance().encrypt(appeal.getEmail()));
            record.set("ADDRESS", PhoneCryptor.getInstance().encrypt(appeal.getAddress()));
            record.set("APPEAL_SOURCE", appeal.getAppealSource());
            record.set("POST_CODE", appeal.getPostCode());
            record.set("COMPLAIN_TIME", appeal.getComplainTime());
            record.set("IP_ADDRESS", appeal.getIpAddress());
            record.set("IS_SYSN", appeal.getIsSysn());
            record.set("SERVICE_STATUS", appeal.getServiceStatus());
            record.set("IS_APPEAL", appeal.getIsAppeal());
            record.set("ORG_CODE",appeal.getOrgCode());
            record.set("ORG_NAME", appeal.getOrgName());
            record.set("EXIST_ATTACHMENT",appeal.getExistAttachment());
            if (StringUtils.isBlank(appeal.getSerialId())) {
                appeal.setSerialId(generateAppealId(appeal.getAppealSource()));
            }
            record.set("SERIAL_ID", appeal.getSerialId());
            try {
                logger.info("[更新申诉请求参数]：" + JSONObject.toJSONString(record));
                QueryFactory.getWriteQuery().update(record);
            } catch (SQLException e) {
                String message = e.getMessage();
                logger.error(message, e);
                return createResult("500", "更新申诉失败");
            }
            return result;
        });
    }

    /**
     * 取消申诉

     * @param id
     * @return
     * @throws SQLException
     */
    public JSONObject cancelAppeal(String id) throws SQLException {
        JSONObject appeal = getAppealInfo(id);
        if (appeal == null) {
            return EasyResult.fail( "申诉信息不存在");
        }
        if (!Constants.SERVICE_STATUS_00.equals(appeal.getString("serviceStatus")) && !Constants.SERVICE_STATUS_01.equals(appeal.getString("serviceStatus"))){
            return EasyResult.fail( "当前工单状态已发生变化，请重新查询。");
        }
        EasyRecord record = new EasyRecord(getTableName("c_box_appeal"),"ID");
        record.set("ID", id);
        record.set("SERVICE_STATUS", Constants.MEDIATION_STATUS_04);
        record.set("IS_CANCEL", Constants.IS_CANCEL_YES);
        record.set("UPDATE_TIME", DateUtil.getCurrentDateStr());
        QueryFactory.getWriteQuery().update(record);
        MsgQueueService.getInstance().add(new JSONObject(){{put("id",id);put("command","cancel");}});
        return EasyResult.ok();
    }

    /**
     * 更新申诉状态
     * @param id
     * @param status
     * @return
     * @throws SQLException
     */
    public JSONObject updateAppealStatus(String id,String status) throws SQLException {
        EasyRecord record = new EasyRecord(getTableName("c_box_appeal"),"ID");
        record.set("ID", id);
        record.set("SERVICE_STATUS", status);
        QueryFactory.getWriteQuery().update(record);
        return EasyResult.ok();
    }

    /**
     * 查询申诉列表
     * @param param
     * @return
     */

    public JSONObject list (JSONObject param) {
        try {
            logger.info("[查询申诉列表请求参数]：" + param.toJSONString());
            EasySQL sql = new EasySQL();
            sql.append("select t1.* ,(case when t2.ID is not null then '01' else '02' end) IS_MEDIATE from " + getTableName("c_box_appeal t1"));
            sql.append("left join " + getTableName("c_box_mediation t2") + " on t2.COMPLAINT_ID = t1.ID");
            sql.append("where 1=1");
            sql.append(Constants.IS_APPEAL_YES," and t1.IS_APPEAL = ? ");
            // 添加查询条件
            String phone = param.getString("phone");
            sql.append(PhoneCryptor.getInstance().encrypt(phone)," and t1.PHONE = ?",false);
            String appealPhone = param.getString("appealPhone");
            sql.append(PhoneCryptor.getInstance().encrypt(appealPhone)," and t1.APPEAL_PHONE = ?",false);
            String startTime = param.getString("startTime");
            
            if (startTime != null && !"".equals(startTime)) {
                sql.append(startTime," and t1.APPEAL_TIME >= ?");
            }

            String endTime = param.getString("endTime");
            if (endTime!= null &&!"".equals(endTime)) {
                sql.append( endTime," and t1.APPEAL_TIME <=?");
            }

            String enableMediate = param.getString("enableMediate");
            if (enableMediate!= null && Constants.YES.equals(enableMediate)) {
                sql.append( " and  t2.ID is null");
                sql.append( Constants.SERVICE_STATUS_06," and  t1.SERVICE_STATUS = ? ");
            }

            String isCancel = param.getString("isCancel");
            if (isCancel!= null &&!"".equals(isCancel)) {
                sql.append( isCancel," and (t1.IS_CANCEL != ? or t1.IS_CANCEL is null)");
                if (Constants.IS_CANCEL_YES.equals(isCancel)) {
                    sql.appendIn(new String[]{Constants.SERVICE_STATUS_00,Constants.SERVICE_STATUS_01}," and t1.SERVICE_STATUS ");
                }
            }
             sql.append( Constants.SERVICE_STATUS_13," and  t1.SERVICE_STATUS != ? ");
            // 添加排序
            sql.append(" order by CREATE_TIME desc");
            logger.info("[查询申诉列表请求参数]：" + sql.getFullSql());
            return this.queryForPageList(sql.getSQL(), sql.getParams(), param, QueryFactory.getReadQuery());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail("查询失败:" + e.getMessage());
        }
    }
    
    /**
	 * 三要素验证
	 * 
	 * @param appeal 请求参数
	 * @param result     结果对象
	 * @return 验证是否通过
	 */
	public boolean verifyThreeElements(BoxAppeal appeal, JSONObject result) {
		String phone = appeal.getPhone();// 申诉手机号;
		String idNumber = appeal.getIdCard();// 身份证;
		String name = appeal.getAppealName();
		String cacheName = phone + idNumber + name;
		JSONObject resultObjThree = CacheUtil.get(cacheName);
		if (resultObjThree == null || StringUtils.isBlank(resultObjThree.getString("result"))) {
            result.putAll(createResult("500", "姓名手机号和身份证未验证,请先验证！"));;
			return true;
		}
		if ("0".equals(resultObjThree.getString("result"))) {
            result.putAll(createResult("500", "姓名手机号和身份证不一致,请重新验证！"));;
			return true;
		}
		CacheUtil.delete(cacheName);
		return false;
	}

    // 三要素验证
	public JSONObject threeFactorVerification(String idType,String idNumber,String name,String phone) {
		try {
			if (StringUtils.isAnyBlank(phone, idType,idNumber, name)) {
                logger.info("[缺少三要素验证请求参数]：申诉手机号、证件类型、证件号码、姓名三者不能为空" );
				return EasyResult.fail("申诉手机号、证件类型、证件号码、姓名三者不能为空");
			}
			String time = DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss SSS");
			JSONObject jsonObject1 = new JSONObject();
			jsonObject1.put("phone", phone);
			jsonObject1.put("name", name);
			jsonObject1.put("idNumber", idNumber);
			jsonObject1.put("idType", idType);// 证件类型 居民身份证
			String data = Sm4Util.encryptEcb(jsonObject1.toString(),Constants.aAuthenticationSecretKey());
			// 字符串的SHA256值
			StringBuffer sb = new StringBuffer();
			sb.append("userId").append(Constants.aAuthenticationUserId());
			sb.append("timestamp").append(time);
			sb.append("data").append(data);
			sb.append(Constants.aAuthenticationSecretKey());
			String sign = Sm4Util.getSHA256Str(sb.toString());
			// 发送请求
			JSONObject reqJson = new JSONObject();
			reqJson.put("userId", Constants.aAuthenticationUserId());
			reqJson.put("sign", sign);
			reqJson.put("timestamp", time);
			reqJson.put("data", data);
			logger.info("[三要素验证请求参数]：" + reqJson.toJSONString());
			String postSync = OkHttpUtil.getInstance().postSync(Constants.aAuthenticationAddress(), reqJson.toJSONString(), "application/json",null,false);;
			logger.info("[三要素验证接口返回]：" + postSync);
			//CacheUtil.put(phone + idNumber + name, postSync);
			return JSONObject.parseObject(postSync);
		} catch (Exception e) {
			logger.error("threeFactorVerification error:" + e.getMessage(), e);
			return EasyResult.fail();
		}
	}

    // 实现getAppealById
    public JSONObject getAppealById(String id) {
        try {
            return EasyResult.ok(getAppealInfo(id));
        } catch (SQLException e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail("查询失败:" + e.getMessage());
        }
    }

    public JSONObject getAppealInfo (String id) throws SQLException {
        EasySQL sql = new EasySQL();
            sql.append("select * from " + getTableName("c_box_appeal")).append("where ID = ?");
            return QueryFactory.getReadQuery().queryForRow(sql.getSQL(), new Object[] { id }, new HumpMapper());
    }

    public JSONObject placeVerification (String phone) {
        String time = DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss SSS");
        String data = Sm4Util.encryptEcb(new JSONObject(){{put("phone", phone);}}.toJSONString(),Constants.aAuthenticationSecretKey());
        logger.info("data解密：" + Sm4Util.decryptEcb(data,Constants.aAuthenticationSecretKey()));
        // 字符串的SHA256值
        StringBuffer sb = new StringBuffer();
        sb.append("userId").append(Constants.aAuthenticationUserId());
        sb.append("timestamp").append(time);
        sb.append("data").append(data);
        sb.append(Constants.aAuthenticationSecretKey());
        logger.info("[归属网络查询请求参数]：" + sb.toString());
        String sign = Sm4Util.getSHA256Str(sb.toString());
        // 发送请求
        JSONObject reqJson = new JSONObject();
        reqJson.put("userId", Constants.aAuthenticationUserId());
        reqJson.put("sign", sign);
        reqJson.put("timestamp", time);
        reqJson.put("data", data);
        logger.info("[归属网络查询请求参数]：" + reqJson.toJSONString());
        String postSync;
        try {
            postSync = OkHttpUtil.getInstance().postSync(Constants.placeAuthenticationAddress(), reqJson.toJSONString(), "application/json",null,false);
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            return null;
        }
        logger.info("[归属网络查询接口返回]：" + postSync);
        JSONObject result = JSONObject.parseObject(postSync);
        JSONObject resultData = new JSONObject();
        if (result != null && "200".equals(result.getString("code"))) {
            resultData.put("carrier", formatStatus(result.getString("serviceOperatorId")));
            resultData.put("province", result.getString("province"));
            resultData.put("city", result.getString("city"));
        } else {
            resultData.put("carrier", formatStatus("006"));
            resultData.put("province", "");
            resultData.put("city", "");
        }
        return resultData;
    }

    /**
     * 码号服务平台接口规范 - 号码归属信息查询接口
     * 按照码号服务平台接口规范文档实现的专用方法
     * 
     * @param phone 待查询的手机号码
     * @return 查询结果，包含详细的归属地信息
     */
    public JSONObject queryPhoneLocationByStandard(String phone) {
        logger.info("[码号服务平台接口规范-号码归属信息查询] 开始查询手机号: {}", phone);
        
        try {
            // 1. 构建请求参数（按照码号服务平台接口规范）
            String timestamp = DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss SSS");
            
            // 2. 构建待加密的数据
            JSONObject phoneData = new JSONObject();
            phoneData.put("phone", phone);
            
            // 3. SM4加密数据
            String encryptedData = Sm4Util.encryptEcb(phoneData.toJSONString(),Constants.aAuthenticationSecretKey());
            
            // 4. 构建签名字符串（按照文档规范顺序）
            StringBuilder signBuilder = new StringBuilder();
            signBuilder.append("userId").append(Constants.aAuthenticationUserId())
                      .append("timestamp").append(timestamp)
                      .append("data").append(encryptedData)
                      .append(Constants.aAuthenticationSecretKey());
                      
            
            // 5. 生成SHA256签名
            String signature = Sm4Util.getSHA256Str(signBuilder.toString());
            
            // 6. 构建完整请求报文
            JSONObject requestBody = new JSONObject();
            requestBody.put("userId", Constants.aAuthenticationUserId());
            requestBody.put("timestamp", timestamp);
            requestBody.put("data", encryptedData);
            requestBody.put("sign", signature);
            
            logger.info("[码号服务平台接口规范-号码归属信息查询] 请求参数: {}", requestBody.toJSONString());
            
            // 7. 发送HTTP请求
            String responseBody = OkHttpUtil.getInstance().postSync(
                Constants.placeAuthenticationAddress(), 
                requestBody.toJSONString(), 
                "application/json", 
                null, 
                false
            );
            
            logger.info("[码号服务平台接口规范-号码归属信息查询] 响应结果: {}", responseBody);
            
            // 8. 解析响应结果
            JSONObject response = JSONObject.parseObject(responseBody);
            
            // 9. 按照码号服务平台接口规范处理响应
            return processStandardResponse(response, phone);
            
        } catch (IOException e) {
            logger.error("[码号服务平台接口规范-号码归属信息查询] 网络请求异常: {}", e.getMessage(), e);
            return buildErrorResponse("网络请求失败", phone);
        } catch (Exception e) {
            logger.error("[码号服务平台接口规范-号码归属信息查询] 系统异常: {}", e.getMessage(), e);
            return buildErrorResponse("系统异常", phone);
        }
    }

    public boolean existAttachment (String attachmentId) {
        try {
            EasySQL sql = new EasySQL();
            sql.append("select count(1) from " + getTableName("c_cf_attachment"));
            sql.append(attachmentId,"where BUSI_ID = ? ",false);
            sql.append(" and IS_DEL = 'N'");
            return getQuery().queryForExist(sql.getSQL(),sql.getParams());
        } catch(Exception e) {
            logger.info("查询附件是否存在异常：" + attachmentId);
            return false;
        }
    }
    
    /**
     * 处理码号服务平台接口规范的标准响应
     * 
     * @param response 原始响应
     * @param phone 查询的手机号
     * @return 标准化的响应结果
     */
    private JSONObject processStandardResponse(JSONObject response, String phone) {
        JSONObject result = new JSONObject();
        result.put("phone", phone);
        if (response != null && "200".equals(response.getString("code"))) {
            // 成功响应处理
            String serviceOperatorId = response.getString("serviceOperatorId");
            String province = response.getString("province");
            String city = response.getString("city");
            String areaCode = response.getString("areaCode");
            
            result.put("success", true);
            result.put("code", "200");
            result.put("message", "查询成功");
            
            // 运营商信息
            result.put("carrierCode", serviceOperatorId);
            result.put("carrierName", getStandardCarrierName(serviceOperatorId).replaceAll("（", "(").replaceAll("）", ")"));
            
            // 归属地信息
            result.put("province", province != null ? province : "");
            result.put("city", city != null ? city : "");
            result.put("areaCode", areaCode != null ? areaCode : "");
            
            logger.info("[码号服务平台接口规范-号码归属信息查询] 查询成功 - 手机号: {}, 运营商: {}, 归属地: {}-{}", 
                       phone, result.getString("carrierName"), province, city);
            
        } else {
            // 失败响应处理
            String errorCode = response != null ? response.getString("code") : "500";
            String errorMessage = response != null ? response.getString("message") : "查询失败";
            
            result.put("success", false);
            result.put("code", errorCode);
            result.put("message", errorMessage);
            result.put("carrierCode", "006");
            result.put("carrierName", "未知");
            result.put("province", "");
            result.put("city", "");
            result.put("areaCode", "");
            
            logger.warn("[码号服务平台接口规范-号码归属信息查询] 查询失败 - 手机号: {}, 错误码: {}, 错误信息: {}", 
                       phone, errorCode, errorMessage);
        }
        
        return result;
    }
    
    /**
     * 构建错误响应
     * 
     * @param errorMessage 错误信息
     * @param phone 查询的手机号
     * @return 错误响应结果
     */
    private JSONObject buildErrorResponse(String errorMessage, String phone) {
        JSONObject result = new JSONObject();
        result.put("phone", phone);
        result.put("queryTime", DateUtil.getCurrentDateStr());
        result.put("success", false);
        result.put("code", "500");
        result.put("message", errorMessage);
        result.put("carrierCode", "006");
        result.put("carrierName", "未知");
        result.put("province", "");
        result.put("city", "");
        result.put("areaCode", "");
        return result;
    }
    
    /**
     * 获取标准运营商名称（按照码号服务平台接口规范）
     * 
     * @param carrierCode 运营商编码
     * @return 运营商名称
     */
    private String getStandardCarrierName(String carrierCode) {
        if (carrierCode == null) {
            return "未知";
        }
        switch (carrierCode) {
            case "001":
				return "中国电信";
			case "002":
				return "中国移动";
			case "003":
				return "中国联通";
			case "005":
				return "中国广电";
            case "101":
                return "阿里通信";
            case "102":
                return "北纬蜂巢互联";
            case "103":
                return "迪信通(迪加）";
            case "104":
                return "263网络通信";
            case "105":
                return "国美通信";
            case "106":
                return "互联时代(蓝猫移动)";
            case "107":
                return "华翔联信";
            case "108":
                return "华云互联";
            case "109":
                return "京东通信";
            case "110":
                return "乐语通信(妙more)";
            case "111":
                return "分享通信";
            case "112":
                return "网信移动";
            case "113":
                return "全民优打";
            case "114":
                return "联想调频(懂的通信)";
            case "115":
                return "广州博元讯息";
            case "116":
                return "朗玛移动";
            case "117":
                return "海航通信";
            case "118":
                return "话机世界";
            case "119":
                return "红豆电信";
            case "120":
                return "民生通讯";
            case "121":
                return "青岛丰信";
            case "122":
                return "日日顺通信";
            case "123":
                return "三五互联";
            case "124":
                return "长城移动";
            case "125":
                return "爱施德(U.友)";
            case "126":
                return "中兴视通";
            case "127":
                return "星美移动";
            case "128":
                return "苏宁互联";
            case "129":
                return "蜗牛移动";
            case "130":
                return "天音通信";
            case "131":
                return "小米移动";
            case "132":
                return "银盛通信";
            case "133":
                return "用友通信";
            case "134":
                return "优酷移动(合一信息)";
            case "135":
                return "远特通信(信时空)";
            case "136":
                return "长江时代";
            case "137":
                return "连连科技";
            case "138":
                return "中期移动";
            case "139":
                return "中邮世纪(普泰移动)";
            default:
                return "未知";
        }
    }
    

    public String formatStatus(String name) {
		String result = "";
		switch (name) {
			case "001":
				result = "电信";
				break;
			case "002":
				result = "移动";
				break;
			case "003":
				result = "联通";
				break;
			case "005":
				result = "广电";
				break;
			default:
				result = "其他";
				break;
		}
		return result;
	}


}
